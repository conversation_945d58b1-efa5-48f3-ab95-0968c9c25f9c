<?php
/**
 * تحديث جميع صفحات المدير لتستخدم المكونات المشتركة
 */

// قائمة صفحات المدير التي تحتاج تحديث
$admin_pages = [
    'admin_users.php',
    'admin_activity.php', 
    'admin_reports.php',
    'admin_financial_reports.php',
    'admin_system.php',
    'system_status.php',
    'translation_summary.php',
    'admin_manage_admins.php'
];

echo "<h2>تحديث صفحات المدير</h2>";

foreach ($admin_pages as $page) {
    if (file_exists($page)) {
        echo "<h3>تحديث: $page</h3>";
        
        $content = file_get_contents($page);
        $updated = false;
        
        // البحث عن القائمة الجانبية القديمة واستبدالها
        $old_sidebar_patterns = [
            '/<!-- الشريط الجانبي.*?<\/nav>/s',
            '/<nav class="col-md-3.*?<\/nav>/s',
            '/<nav.*?sidebar.*?<\/nav>/s'
        ];
        
        foreach ($old_sidebar_patterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $content = preg_replace($pattern, '<?php include __DIR__ . \'/includes/admin_sidebar.php\'; ?>', $content);
                $updated = true;
                echo "✅ تم تحديث القائمة الجانبية<br>";
                break;
            }
        }
        
        // البحث عن الشريط العلوي القديم واستبدالها
        $old_navbar_patterns = [
            '/<!-- الشريط العلوي.*?<\/nav>/s',
            '/<nav class="navbar.*?admin-navbar.*?<\/nav>/s'
        ];
        
        foreach ($old_navbar_patterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $content = preg_replace($pattern, '<?php include __DIR__ . \'/includes/admin_navbar.php\'; ?>', $content);
                $updated = true;
                echo "✅ تم تحديث الشريط العلوي<br>";
                break;
            }
        }
        
        // إضافة الشريط العلوي إذا لم يكن موجوداً
        if (!strpos($content, 'admin_navbar.php') && !strpos($content, 'navbar')) {
            $content = str_replace(
                'require_once __DIR__ . \'/includes/admin_header.php\';',
                'require_once __DIR__ . \'/includes/admin_header.php\';\n?>\n\n<?php include __DIR__ . \'/includes/admin_navbar.php\'; ?>',
                $content
            );
            $updated = true;
            echo "✅ تم إضافة الشريط العلوي<br>";
        }
        
        // إضافة القائمة الجانبية إذا لم تكن موجودة
        if (!strpos($content, 'admin_sidebar.php') && !strpos($content, 'sidebar')) {
            $content = str_replace(
                '<div class="row">',
                '<div class="row">\n        <?php include __DIR__ . \'/includes/admin_sidebar.php\'; ?>',
                $content
            );
            $updated = true;
            echo "✅ تم إضافة القائمة الجانبية<br>";
        }
        
        if ($updated) {
            file_put_contents($page, $content);
            echo "✅ تم حفظ التحديثات بنجاح<br>";
        } else {
            echo "ℹ️ لا توجد تحديثات مطلوبة<br>";
        }
        
        echo "<hr>";
    } else {
        echo "❌ الملف غير موجود: $page<br>";
    }
}

echo "<br><strong>تم الانتهاء من تحديث جميع الصفحات!</strong><br>";
echo '<a href="admin_dashboard.php">الذهاب إلى لوحة التحكم</a><br>';
echo '<a href="admin_error_logs.php">الذهاب إلى سجل الأخطاء</a>';
?>
