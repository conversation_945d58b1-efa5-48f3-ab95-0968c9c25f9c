<?php
/**
 * دوال الفواتير ومعلومات الشركة
 */

// تضمين ملف الإعدادات
require_once __DIR__ . '/../config/unified_db_config.php';

/**
 * جلب إعداد من قاعدة البيانات
 */
function getInvoiceSetting($key, $default = '') {
    global $main_db;
    if (!$main_db) {
        $main_db = getUnifiedDB();
    }
    
    $stmt = $main_db->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
    if (!$stmt) {
        return $default;
    }
    
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();
    
    return $row ? $row['setting_value'] : $default;
}

/**
 * عرض رأس الفاتورة مع معلومات الشركة
 */
function displayInvoiceHeader($invoice_type = 'sales') {
    $company_name = getInvoiceSetting('company_name', 'نظام المبيعات');
    $company_name_en = getInvoiceSetting('company_name_en', 'Sales System');
    $company_address = getInvoiceSetting('company_address');
    $company_phone = getInvoiceSetting('company_phone');
    $company_email = getInvoiceSetting('company_email');
    $company_website = getInvoiceSetting('company_website');
    $company_tax_number = getInvoiceSetting('company_tax_number');
    $company_commercial_register = getInvoiceSetting('company_commercial_register');
    $company_logo = getInvoiceSetting('company_logo');
    $show_logo = getInvoiceSetting('show_logo_on_invoices', '1') == '1';
    $show_company_info = getInvoiceSetting('show_company_info_on_invoices', '1') == '1';
    
    $invoice_title = $invoice_type === 'sales' ? 'فاتورة مبيعات' : 'فاتورة مشتريات';
    $invoice_title_en = $invoice_type === 'sales' ? 'Sales Invoice' : 'Purchase Invoice';
    
    echo '<div class="invoice-header">';
    
    if ($show_logo && $company_logo) {
        echo '<div class="company-logo text-center mb-3">';
        echo '<img src="uploads/' . htmlspecialchars($company_logo) . '" alt="شعار الشركة" style="max-height: 80px;">';
        echo '</div>';
    }
    
    echo '<div class="company-info text-center mb-4">';
    echo '<h2 class="company-name mb-1">' . htmlspecialchars($company_name) . '</h2>';
    if ($company_name_en) {
        echo '<h4 class="company-name-en text-muted mb-2">' . htmlspecialchars($company_name_en) . '</h4>';
    }
    
    if ($show_company_info) {
        echo '<div class="company-details">';
        if ($company_address) {
            echo '<p class="mb-1"><i class="fas fa-map-marker-alt me-2"></i>' . htmlspecialchars($company_address) . '</p>';
        }
        
        echo '<div class="contact-info">';
        if ($company_phone) {
            echo '<span class="me-3"><i class="fas fa-phone me-1"></i>' . htmlspecialchars($company_phone) . '</span>';
        }
        if ($company_email) {
            echo '<span class="me-3"><i class="fas fa-envelope me-1"></i>' . htmlspecialchars($company_email) . '</span>';
        }
        if ($company_website) {
            echo '<span><i class="fas fa-globe me-1"></i>' . htmlspecialchars($company_website) . '</span>';
        }
        echo '</div>';
        
        if ($company_tax_number || $company_commercial_register) {
            echo '<div class="legal-info mt-2">';
            if ($company_tax_number) {
                echo '<span class="me-3">الرقم الضريبي: ' . htmlspecialchars($company_tax_number) . '</span>';
            }
            if ($company_commercial_register) {
                echo '<span>السجل التجاري: ' . htmlspecialchars($company_commercial_register) . '</span>';
            }
            echo '</div>';
        }
        echo '</div>';
    }
    echo '</div>';
    
    echo '<div class="invoice-title text-center mb-4">';
    echo '<h3 class="invoice-type">' . $invoice_title . '</h3>';
    if ($invoice_title_en) {
        echo '<h5 class="invoice-type-en text-muted">' . $invoice_title_en . '</h5>';
    }
    echo '</div>';
    
    echo '</div>';
}

/**
 * عرض تذييل الفاتورة
 */
function displayInvoiceFooter() {
    $company_name = getInvoiceSetting('company_name', 'نظام المبيعات');
    $system_version = getInvoiceSetting('system_version', '2.0');
    
    echo '<div class="invoice-footer mt-4 pt-3 border-top text-center">';
    echo '<small class="text-muted">';
    echo 'تم إنشاء هذه الفاتورة بواسطة ' . htmlspecialchars($company_name);
    echo ' - إصدار ' . htmlspecialchars($system_version);
    echo ' - ' . date('Y-m-d H:i:s');
    echo '</small>';
    echo '</div>';
}

/**
 * الحصول على بادئة رقم الفاتورة
 */
function getInvoicePrefix($type = 'sales') {
    if ($type === 'sales') {
        return getInvoiceSetting('invoice_prefix', 'INV');
    } else {
        return getInvoiceSetting('purchase_invoice_prefix', 'PUR');
    }
}

/**
 * الحصول على رقم الفاتورة التالي
 */
function getNextInvoiceNumber($type = 'sales') {
    global $main_db;
    if (!$main_db) {
        $main_db = getUnifiedDB();
    }
    
    $table = $type === 'sales' ? 'sales' : 'purchases';
    $prefix = getInvoicePrefix($type);
    
    // جلب آخر رقم فاتورة
    $stmt = $main_db->prepare("SELECT MAX(CAST(SUBSTRING(invoice_number, LENGTH(?) + 1) AS UNSIGNED)) as last_number FROM $table WHERE invoice_number LIKE CONCAT(?, '%')");
    $stmt->bind_param("ss", $prefix, $prefix);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();
    
    $last_number = $row['last_number'] ?? 0;
    $next_number = $last_number + 1;
    
    return $prefix . str_pad($next_number, 6, '0', STR_PAD_LEFT);
}

/**
 * تنسيق العملة
 */
function formatCurrency($amount) {
    $currency_symbol = getInvoiceSetting('currency_symbol', 'ريال');
    $decimal_places = intval(getInvoiceSetting('decimal_places', '2'));
    
    return number_format($amount, $decimal_places) . ' ' . $currency_symbol;
}

/**
 * CSS للفواتير
 */
function getInvoiceCSS() {
    return '
    <style>
    .invoice-header {
        margin-bottom: 30px;
    }
    
    .company-logo img {
        max-height: 80px;
        width: auto;
    }
    
    .company-name {
        color: #2c3e50;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .company-name-en {
        color: #7f8c8d;
        font-size: 1.1rem;
        margin-bottom: 15px;
    }
    
    .company-details {
        color: #34495e;
        font-size: 0.9rem;
    }
    
    .contact-info {
        margin: 10px 0;
    }
    
    .legal-info {
        font-size: 0.8rem;
        color: #7f8c8d;
    }
    
    .invoice-title {
        border-bottom: 2px solid #3498db;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    
    .invoice-type {
        color: #2c3e50;
        font-weight: bold;
    }
    
    .invoice-type-en {
        color: #7f8c8d;
    }
    
    .invoice-footer {
        border-top: 1px solid #ecf0f1;
        margin-top: 30px;
        padding-top: 15px;
    }
    
    @media print {
        .invoice-header, .invoice-footer {
            color: #000 !important;
        }
        
        .company-logo img {
            max-height: 60px;
        }
    }
    </style>';
}

/**
 * جافا سكريبت للفواتير
 */
function getInvoiceJS() {
    return '
    <script>
    function printInvoice() {
        window.print();
    }
    
    function downloadInvoicePDF() {
        // يمكن تطوير هذه الوظيفة لاحقاً
        alert("سيتم تطوير تحميل PDF قريباً");
    }
    
    function emailInvoice() {
        // يمكن تطوير هذه الوظيفة لاحقاً
        alert("سيتم تطوير إرسال البريد الإلكتروني قريباً");
    }
    </script>';
}
?>
