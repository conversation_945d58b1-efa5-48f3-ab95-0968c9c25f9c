<?php
/**
 * صفحة التقارير المالية وتفاصيل الفواتير
 */
require_once __DIR__ . '/config/init.php';
// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}
// التحقق من الصلاحيات
if (!hasAdminPermission('view_all_data')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}
// تضمين ملف قاعدة البيانات الموحدة
require_once __DIR__ . '/config/unified_db_config.php';
$main_db = getUnifiedDB();
// معاملات البحث
$user_filter = $_GET['user_id'] ?? '';
$date_from = $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$report_type = $_GET['report_type'] ?? 'summary';
// التأكد من أن التواريخ صحيحة
if (empty($date_from)) $date_from = date('Y-m-01');
if (empty($date_to)) $date_to = date('Y-m-d');
// إضافة تشخيص
$debug_mode = isset($_GET['debug']) && $_GET['debug'] == '1';
// جلب قائمة المستخدمين من قاعدة البيانات الموحدة
$users_result = null;
if ($main_db) {
    $users_result = $main_db->query("SELECT id, username, full_name FROM users WHERE status = 'active' ORDER BY full_name");
    if (!$users_result) {
        // إذا فشل الاستعلام، إنشاء نتيجة فارغة
        $users_result = new class {
            public function fetch_assoc() {
 return null; }
        };
}
}
else {
    // إذا فشل الاتصال، إنشاء نتيجة فارغة
    $users_result = new class {
        public function fetch_assoc() {
 return null; }
    };
}
// جلب البيانات المالية
$financial_data = [];
$total_sales = 0;
$total_purchases = 0;
$total_profit = 0;
// استخدام قاعدة البيانات الموحدة
require_once __DIR__ . '/config/unified_db_config.php';
$user_db = getUnifiedDB();
// تهيئة المتغيرات
$sales_result = null;
$purchases_result = null;
$total_sales = 0;
$total_purchases = 0;
$total_profit = 0;
if ($user_db) {
    try {
        if ($debug_mode) {
            echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc;'>";
            echo "<h4>معلومات التشخيص:</h4>";
            echo "المستخدم المحدد: " . ($user_filter ?: 'جميع المستخدمين') . "<br>";
            echo "من تاريخ: $date_from<br>";
            echo "إلى تاريخ: $date_to<br>";
            echo "نوع التقرير: $report_type<br>";
            echo "</div>";
}
        if ($user_filter) {
            // تقرير مستخدم محدد
            if ($debug_mode) echo "<p>جاري جلب بيانات المستخدم رقم: $user_filter</p>
";
            // جلب بيانات المبيعات للمستخدم المحدد
            $sales_query = "SELECT s.*, c.name as customer_name, u.full_name as user_name
                           FROM sales s
                           LEFT JOIN customers c ON s.customer_id = c.id
                           LEFT JOIN users u ON s.user_id = u.id
                           WHERE s.user_id = ? AND s.date BETWEEN ? AND ?
                           ORDER BY s.date DESC";
            $sales_stmt = $user_db->prepare($sales_query);
            if ($sales_stmt) {
                $sales_stmt->bind_param("iss", $user_filter, $date_from, $date_to);
                $sales_stmt->execute();
                $sales_result = $sales_stmt->get_result();
                if ($debug_mode) echo "<p>عدد فواتير المبيعات: " . $sales_result->num_rows . "</p>
";
}
else {
                if ($debug_mode) echo "<p>خطأ في استعلام المبيعات: " . $user_db->error . "</p>
";
}
            // جلب بيانات المشتريات للمستخدم المحدد
            $purchases_query = "SELECT p.*, p.supplier_name, u.full_name as user_name
                               FROM purchases p
                               LEFT JOIN users u ON p.user_id = u.id
                               WHERE p.user_id = ? AND p.date BETWEEN ? AND ?
                               ORDER BY p.date DESC";
            $purchases_stmt = $user_db->prepare($purchases_query);
            if ($purchases_stmt) {
                $purchases_stmt->bind_param("iss", $user_filter, $date_from, $date_to);
                $purchases_stmt->execute();
                $purchases_result = $purchases_stmt->get_result();
                if ($debug_mode) echo "<p>عدد فواتير المشتريات: " . $purchases_result->num_rows . "</p>
";
}
else {
                if ($debug_mode) echo "<p>خطأ في استعلام المشتريات: " . $user_db->error . "</p>
";
}
            // حساب الإجماليات للمستخدم المحدد
            $sales_total_stmt = $user_db->prepare("SELECT SUM(total_amount) as total FROM sales WHERE user_id = ? AND date BETWEEN ? AND ?");
            if ($sales_total_stmt) {
                $sales_total_stmt->bind_param("iss", $user_filter, $date_from, $date_to);
                $sales_total_stmt->execute();
                $sales_total_result = $sales_total_stmt->get_result();
                $total_sales = $sales_total_result->fetch_assoc()['total'] ?? 0;
                if ($debug_mode) echo "<p>إجمالي مبيعات المستخدم: " . number_format($total_sales, 2) . "</p>
";
                $sales_total_stmt->close();
}
            $purchases_total_stmt = $user_db->prepare("SELECT SUM(total_amount) as total FROM purchases WHERE user_id = ? AND date BETWEEN ? AND ?");
            if ($purchases_total_stmt) {
                $purchases_total_stmt->bind_param("iss", $user_filter, $date_from, $date_to);
                $purchases_total_stmt->execute();
                $purchases_total_result = $purchases_total_stmt->get_result();
                $total_purchases = $purchases_total_result->fetch_assoc()['total'] ?? 0;
                if ($debug_mode) echo "<p>إجمالي مشتريات المستخدم: " . number_format($total_purchases, 2) . "</p>
";
                $purchases_total_stmt->close();
}
            $total_profit = $total_sales - $total_purchases;
            // إغلاق الاستعلامات
            if ($sales_stmt) $sales_stmt->close();
            if ($purchases_stmt) $purchases_stmt->close();
}
else {
            // تقرير جميع المستخدمين
            if ($debug_mode) echo "<p>جاري جلب بيانات جميع المستخدمين</p>
";
            // جلب بيانات المبيعات لجميع المستخدمين
            $sales_query = "SELECT s.*, c.name as customer_name, u.full_name as user_name
                           FROM sales s
                           LEFT JOIN customers c ON s.customer_id = c.id
                           LEFT JOIN users u ON s.user_id = u.id
                           WHERE s.date BETWEEN ? AND ?
                           ORDER BY s.date DESC";
            $sales_stmt = $user_db->prepare($sales_query);
            if ($sales_stmt) {
                $sales_stmt->bind_param("ss", $date_from, $date_to);
                $sales_stmt->execute();
                $sales_result = $sales_stmt->get_result();
                if ($debug_mode) echo "<p>عدد فواتير المبيعات (جميع المستخدمين): " . $sales_result->num_rows . "</p>
";
}
else {
                if ($debug_mode) echo "<p>خطأ في استعلام المبيعات: " . $user_db->error . "</p>
";
}
            // جلب بيانات المشتريات لجميع المستخدمين
            $purchases_query = "SELECT p.*, p.supplier_name, u.full_name as user_name
                               FROM purchases p
                               LEFT JOIN users u ON p.user_id = u.id
                               WHERE p.date BETWEEN ? AND ?
                               ORDER BY p.date DESC";
            $purchases_stmt = $user_db->prepare($purchases_query);
            if ($purchases_stmt) {
                $purchases_stmt->bind_param("ss", $date_from, $date_to);
                $purchases_stmt->execute();
                $purchases_result = $purchases_stmt->get_result();
                if ($debug_mode) echo "<p>عدد فواتير المشتريات (جميع المستخدمين): " . $purchases_result->num_rows . "</p>
";
}
else {
                if ($debug_mode) echo "<p>خطأ في استعلام المشتريات: " . $user_db->error . "</p>
";
}
            // حساب الإجماليات لجميع المستخدمين
            $sales_total_stmt = $user_db->prepare("SELECT SUM(total_amount) as total FROM sales WHERE date BETWEEN ? AND ?");
            if ($sales_total_stmt) {
                $sales_total_stmt->bind_param("ss", $date_from, $date_to);
                $sales_total_stmt->execute();
                $sales_total_result = $sales_total_stmt->get_result();
                $total_sales = $sales_total_result->fetch_assoc()['total'] ?? 0;
                if ($debug_mode) echo "<p>إجمالي مبيعات جميع المستخدمين: " . number_format($total_sales, 2) . "</p>
";
                $sales_total_stmt->close();
}
            $purchases_total_stmt = $user_db->prepare("SELECT SUM(total_amount) as total FROM purchases WHERE date BETWEEN ? AND ?");
            if ($purchases_total_stmt) {
                $purchases_total_stmt->bind_param("ss", $date_from, $date_to);
                $purchases_total_stmt->execute();
                $purchases_total_result = $purchases_total_stmt->get_result();
                $total_purchases = $purchases_total_result->fetch_assoc()['total'] ?? 0;
                if ($debug_mode) echo "<p>إجمالي مشتريات جميع المستخدمين: " . number_format($total_purchases, 2) . "</p>
";
                $purchases_total_stmt->close();
}
            $total_profit = $total_sales - $total_purchases;
            // إغلاق الاستعلامات
            if ($sales_stmt) $sales_stmt->close();
            if ($purchases_stmt) $purchases_stmt->close();
}
    }
catch (Exception $e) {
        // تسجيل الخطأ
        error_log("خطأ في التقارير المالية: " . $e->getMessage());
        $total_sales = 0;
        $total_purchases = 0;
        $total_profit = 0;
}
}
require_once __DIR__ . '/includes/admin_header_new.php';
?>
<div class="admin-layout">
        <!-- الشريط الجانبي -->
        <nav class="modern-sidebar d-none d-lg-block">
            <div class="sidebar-section">
                <div class="sidebar-section-title">الإدارة الرئيسية</div>
                <a class="sidebar-nav-link" href="admin_dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
                <a class="sidebar-nav-link" href="admin_users.php">
                    <i class="fas fa-users"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                <a class="sidebar-nav-link" href="admin_activity.php">
                    <i class="fas fa-history"></i>
                    <span>سجل العمليات</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">التقارير والإحصائيات</div>
                <a class="sidebar-nav-link" href="admin_reports.php">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير الشاملة</span>
                </a>
                <a class="sidebar-nav-link" href="admin_financial.php">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <span>التقارير المالية</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة النظام</div>
                <a class="sidebar-nav-link" href="admin_error_logs.php">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>سجل الأخطاء</span>
                </a>
                <a class="sidebar-nav-link" href="admin_system.php">
                    <i class="fas fa-cogs"></i>
                    <span>إعدادات النظام</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة المديرين</div>
                <a class="sidebar-nav-link" href="admin_manage_admins.php">
                    <i class="fas fa-user-shield"></i>
                    <span>إدارة المديرين</span>
                </a>
            </div>
        </nav>
        <!-- المحتوى الرئيسي -->
        <main class="admin-content">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-2 fw-bold gradient-text">
                        <i class="fas fa-file-invoice-dollar me-3"></i>
                        التقارير المالية
                    </h1>
                    <p class="text-muted mb-0">مراجعة التقارير المالية والإحصائيات</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="modern-btn modern-btn-outline" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i>
                        <span>تحديث</span>
                    </button>
                </div>
            </div>
                </div>
            </div>
            <!-- فلاتر التقرير -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="mb-0"><i class="fas fa-filter me-2"></i>فلاتر التقرير المالي</h5>
                </div>
                <div class="modern-card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="user_id" class="modern-form-label">المستخدم</label>
                            <select class="modern-form-control" id="user_id" name="user_id">
                                <option value="">جميع المستخدمين</option>
                                <?php while ($user = $users_result->fetch_assoc()): ?>
                                <option value="<?php echo $user['id']; ?>" <?php echo $user_filter == $user['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($user['full_name']); ?>
                                </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="report_type" class="modern-form-label">نوع التقرير</label>
                            <select class="modern-form-control" id="report_type" name="report_type">
                                <option value="summary" <?php echo $report_type === 'summary' ? 'selected' : ''; ?>>ملخص</option>
                                <option value="detailed" <?php echo $report_type === 'detailed' ? 'selected' : ''; ?>>تفصيلي</option>
                                <option value="invoices" <?php echo $report_type === 'invoices' ? 'selected' : ''; ?>>الفواتير</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="modern-form-label">من تاريخ</label>
                            <input type="date" class="modern-form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="modern-form-label">إلى تاريخ</label>
                            <input type="date" class="modern-form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="modern-btn modern-btn-danger">
                                <i class="fas fa-search me-1"></i>إنشاء التقرير
                            </button>
                        </div>
</form>
                </div>
            </div>
            <?php if (isset($total_sales)): ?>
            <!-- الملخص المالي -->
            <div class="row mb-4">
                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-1">
                                <div class="stats-label">
                                    إجمالي المبيعات
                                </div>
                                <div class="stats-value text-success">
                                    <?php echo number_format($total_sales, 2); ?> ريال
                                </div>
                                <div class="stats-change text-muted">
                                    <i class="fas fa-arrow-up me-1"></i>
                                    إيرادات
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-1">
                                <div class="stats-label">
                                    إجمالي المشتريات
                                </div>
                                <div class="stats-value text-danger">
                                    <?php echo number_format($total_purchases, 2); ?> ريال
                                </div>
                                <div class="stats-change text-muted">
                                    <i class="fas fa-arrow-down me-1"></i>
                                    مصروفات
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-1">
                                <div class="stats-label">
                                    صافي الربح
                                </div>
                                <div class="stats-value <?php echo $total_profit >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    <?php echo number_format($total_profit, 2); ?> ريال
                                </div>
                                <div class="stats-change text-muted">
                                    <i class="fas fa-chart-line me-1"></i>
                                    <?php echo $total_profit >= 0 ? 'ربح' : 'خسارة'; ?>
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-coins"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php if ($report_type === 'invoices' && isset($sales_result)): ?>
            <!-- تفاصيل فواتير المبيعات -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-file-invoice me-2"></i>
                        فواتير المبيعات
                    </h5>
                </div>
                <div class="modern-card-body">
                    <div class="modern-table table-responsive">
                        <table class="modern-table table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>العميل</th>
                                    <?php if (!$user_filter): ?>
                                    <th>المستخدم</th>
                                    <?php endif; ?>
                                    <th>المبلغ الفرعي</th>
                                    <th>الضريبة</th>
                                    <th>الإجمالي</th>
                                    <th>الإجراءات</th>
</tr>
                            </thead>
                            <tbody>
                                <?php while ($sale = $sales_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($sale['invoice_number']); ?></td>
                                    <td><?php echo date('Y-m-d', strtotime($sale['date'])); ?></td>
                                    <td><?php echo htmlspecialchars($sale['customer_name'] ?? 'غير محدد'); ?></td>
                                    <?php if (!$user_filter): ?>
                                    <td><?php echo htmlspecialchars($sale['user_name'] ?? 'غير محدد'); ?></td>
                                    <?php endif; ?>
                                    <td><?php echo number_format($sale['subtotal'], 2); ?></td>
                                    <td><?php echo number_format($sale['tax_amount'], 2); ?></td>
                                    <td><strong><?php echo number_format($sale['total_amount'], 2); ?></strong></td>
                                    <td>
                                        <button class="modern-btn modern-btn-sm btn-info" onclick="viewInvoiceDetails(<?php echo $sale['id']; ?>, 'sale')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
</tr>
                                <?php endwhile; ?>
                            </tbody>
</table>
                    </div>
                </div>
            </div>
            <!-- تفاصيل فواتير المشتريات -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-file-invoice me-2"></i>
                        فواتير المشتريات
                    </h5>
                </div>
                <div class="modern-card-body">
                    <div class="modern-table table-responsive">
                        <table class="modern-table table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>المورد</th>
                                    <?php if (!$user_filter): ?>
                                    <th>المستخدم</th>
                                    <?php endif; ?>
                                    <th>المبلغ الفرعي</th>
                                    <th>الضريبة</th>
                                    <th>الإجمالي</th>
                                    <th>الإجراءات</th>
</tr>
                            </thead>
                            <tbody>
                                <?php if (isset($purchases_result)): ?>
<?php while ($purchase = $purchases_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($purchase['invoice_number']); ?></td>
                                    <td><?php echo date('Y-m-d', strtotime($purchase['date'])); ?></td>
                                    <td><?php echo htmlspecialchars($purchase['supplier_name'] ?? 'غير محدد'); ?></td>
                                    <?php if (!$user_filter): ?>
                                    <td><?php echo htmlspecialchars($purchase['user_name'] ?? 'غير محدد'); ?></td>
                                    <?php endif; ?>
                                    <td><?php echo number_format($purchase['subtotal'], 2); ?></td>
                                    <td><?php echo number_format($purchase['tax_amount'], 2); ?></td>
                                    <td><strong><?php echo number_format($purchase['total_amount'], 2); ?></strong></td>
                                    <td>
                                        <button class="modern-btn modern-btn-sm btn-info" onclick="viewInvoiceDetails(<?php echo $purchase['id']; ?>, 'purchase')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
</tr>
                                <?php endwhile; ?>
<?php endif; ?>
                            </tbody>
</table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
<?php else: ?>
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>اضغط "إنشاء التقرير" لعرض البيانات المالية</h5>
                <p>يمكنك اختيار مستخدم محدد أو ترك الحقل فارغاً لعرض تقرير جميع المستخدمين، ثم اضغط "إنشاء التقرير".</p>
            </div>
            <?php endif; ?>
        </main></div>
<script>
function viewInvoiceDetails(invoiceId, type) {
    // سيتم إضافة نافذة منبثقة لعرض تفاصيل الفاتورة
    alert('سيتم إضافة عرض تفاصيل الفاتورة قريباً');
}

function exportFinancialReport() {
    // سيتم إضافة وظيفة تصدير التقرير المالي
    alert('سيتم إضافة وظيفة التصدير قريباً');
}
</script>
<?php require_once __DIR__ . '/includes/admin_footer_new.php'; ?>