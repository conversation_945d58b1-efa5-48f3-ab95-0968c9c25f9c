<?php
/**
 * الشريط العلوي الموحد لقسم المدير
 * يتم تضمينه في جميع صفحات المدير لضمان التوحيد
 */
?>

<!-- الشريط العلوي المحسن -->
<nav class="navbar navbar-expand-lg admin-navbar">
    <div class="container-fluid">
        <a class="navbar-brand" href="admin_dashboard.php">
            <i class="fas fa-shield-alt"></i>
            نظام إدارة المبيعات - المدير
        </a>
        
        <div class="navbar-nav ms-auto">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user-circle"></i>
                    <span><?php echo $_SESSION['admin_username'] ?? 'المدير'; ?></span>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="admin_profile.php"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                    <li><a class="dropdown-item" href="admin_settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="admin_logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                </ul>
            </div>
            
            <button class="btn btn-outline-secondary btn-sm ms-2" onclick="toggleTheme()" title="تبديل الوضع">
                <i class="fas fa-moon" id="theme-icon"></i>
            </button>
        </div>
    </div>
</nav>

<script>
// دالة تبديل الوضع الداكن/الفاتح
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('admin-theme', newTheme);
    
    // تحديث أيقونة الزر
    const themeIcon = document.getElementById('theme-icon');
    if (newTheme === 'dark') {
        themeIcon.className = 'fas fa-sun';
    } else {
        themeIcon.className = 'fas fa-moon';
    }
}

// دالة تحميل الوضع المحفوظ
function loadTheme() {
    const savedTheme = localStorage.getItem('admin-theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    
    const themeIcon = document.getElementById('theme-icon');
    if (savedTheme === 'dark') {
        themeIcon.className = 'fas fa-sun';
    } else {
        themeIcon.className = 'fas fa-moon';
    }
}

// تحميل الوضع عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadTheme();
});
</script>
