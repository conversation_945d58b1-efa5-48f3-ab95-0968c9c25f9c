<!-- تذييل الصفحة المتطور -->
<footer class="mt-5 py-4 text-center text-muted" style="border-top: 1px solid rgba(0,0,0,0.1);">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6 text-md-start">
                <p class="mb-0">
                    <i class="fas fa-copyright me-1"></i>
                    <?php echo date('Y'); ?> - <?php echo htmlspecialchars(getSystemSetting('system_name', 'نظام إدارة المبيعات')); ?>
                </p>
            </div>
            <div class="col-md-6 text-md-end">
                <p class="mb-0">
                    <i class="fas fa-code me-1"></i>
                    تم التطوير بواسطة فريق التطوير
                </p>
            </div>
        </div>
    </div>
</footer>

<!-- إضافة JavaScript للتفاعلات المتطورة -->
<script>
// تأثيرات التحميل
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثير fade-in للعناصر
    const elements = document.querySelectorAll('.stats-card, .modern-card');
    elements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.6s ease-out';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // تحديث الوقت كل ثانية
    updateTime();
    setInterval(updateTime, 1000);
});

// دالة تحديث الوقت
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// تحسين الاستجابة للشاشات الصغيرة
function adjustLayoutForMobile() {
    const mainContent = document.getElementById('main-content');
    const sidebar = document.querySelector('.modern-sidebar');
    
    if (window.innerWidth <= 992) {
        if (mainContent) {
            mainContent.style.marginRight = '0';
        }
    } else {
        if (mainContent) {
            mainContent.style.marginRight = '300px';
        }
    }
}

// استدعاء دالة التخطيط عند تغيير حجم النافذة
window.addEventListener('resize', adjustLayoutForMobile);
window.addEventListener('load', adjustLayoutForMobile);

// تحسين أداء الرسوم المتحركة
function optimizeAnimations() {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    if (prefersReducedMotion.matches) {
        // تقليل الرسوم المتحركة للمستخدمين الذين يفضلون ذلك
        document.documentElement.style.setProperty('--transition-base', 'none');
        document.documentElement.style.setProperty('--transition-fast', 'none');
        document.documentElement.style.setProperty('--transition-slow', 'none');
    }
}

optimizeAnimations();

// إضافة تأثيرات تفاعلية للبطاقات
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.stats-card, .modern-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});

// تحسين تجربة المستخدم مع الأزرار
document.addEventListener('DOMContentLoaded', function() {
    const buttons = document.querySelectorAll('.modern-btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // إضافة تأثير الضغط
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    /* تحسينات إضافية للوضع الداكن */
    [data-theme="dark"] .ripple {
        background: rgba(255, 255, 255, 0.3);
    }
    
    /* تحسين الشريط الجانبي للشاشات الصغيرة */
    @media (max-width: 992px) {
        #main-content {
            margin-right: 0 !important;
            padding: 1rem !important;
        }
        
        .modern-sidebar {
            transform: translateX(-100%);
        }
        
        .modern-sidebar.show {
            transform: translateX(0);
        }
    }
`;
document.head.appendChild(style);
</script>

</body>
</html>
