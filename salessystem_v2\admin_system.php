<?php
/**
 * صفحة إعدادات النظام للمدير
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// التحقق من الصلاحيات
if (!hasAdminPermission('manage_system')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}

global $main_db;

// التحقق من اتصال قاعدة البيانات
if (!$main_db) {
    error_log("Database connection failed in admin_system.php");
    die('خطأ في الاتصال بقاعدة البيانات');
}

// إنشاء جدول الإعدادات إذا لم يكن موجوداً
$create_settings_table = "CREATE TABLE IF NOT EXISTS `system_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `setting_key` varchar(100) NOT NULL UNIQUE,
    `setting_value` text DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if (!$main_db->query($create_settings_table)) {
    error_log("Failed to create system_settings table: " . $main_db->error);
}

// دالة لجلب إعداد
function getSetting($key, $default = '') {
    global $main_db;

    if (!$main_db) {
        error_log("Database connection not available in getSetting");
        return $default;
    }

    $stmt = $main_db->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
    if (!$stmt) {
        error_log("Failed to prepare getSetting query: " . $main_db->error);
        return $default;
    }

    $stmt->bind_param("s", $key);
    if (!$stmt->execute()) {
        error_log("Failed to execute getSetting query: " . $stmt->error);
        $stmt->close();
        return $default;
    }

    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();

    return $row ? $row['setting_value'] : $default;
}

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'update_sales_settings':
            // تحديث إعدادات المبيعات
            $settings = [
                'default_tax_rate' => floatval($_POST['default_tax_rate'] ?? 15),
                'invoice_prefix' => trim($_POST['invoice_prefix'] ?? 'INV'),
                'auto_invoice_number' => isset($_POST['auto_invoice_number']) ? 1 : 0,
                'require_customer' => isset($_POST['require_customer']) ? 1 : 0,
                'allow_negative_stock' => isset($_POST['allow_negative_stock']) ? 1 : 0,
                'default_payment_method' => trim($_POST['default_payment_method'] ?? 'cash'),
                'currency_symbol' => trim($_POST['currency_symbol'] ?? 'ريال'),
                'decimal_places' => intval($_POST['decimal_places'] ?? 2)
            ];

            foreach ($settings as $key => $value) {
                $stmt = $main_db->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
                $stmt->bind_param("sss", $key, $value, $value);
                $stmt->execute();
                $stmt->close();
            }

            logActivity('sales_settings_updated', 'system_settings', null, null, null, 'تم تحديث إعدادات المبيعات');
            $_SESSION['success'] = 'تم تحديث إعدادات المبيعات بنجاح';
            break;

        case 'update_purchase_settings':
            // تحديث إعدادات المشتريات
            $settings = [
                'purchase_invoice_prefix' => trim($_POST['purchase_invoice_prefix'] ?? 'PUR'),
                'auto_purchase_number' => isset($_POST['auto_purchase_number']) ? 1 : 0,
                'require_supplier' => isset($_POST['require_supplier']) ? 1 : 0,
                'auto_update_stock' => isset($_POST['auto_update_stock']) ? 1 : 0,
                'purchase_approval_required' => isset($_POST['purchase_approval_required']) ? 1 : 0
            ];

            foreach ($settings as $key => $value) {
                $stmt = $main_db->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
                $stmt->bind_param("sss", $key, $value, $value);
                $stmt->execute();
                $stmt->close();
            }

            logActivity('purchase_settings_updated', 'system_settings', null, null, null, 'تم تحديث إعدادات المشتريات');
            $_SESSION['success'] = 'تم تحديث إعدادات المشتريات بنجاح';
            break;

        case 'update_company_info':
            try {
                // تحديث معلومات الشركة/البرنامج
                $settings = [
                    'company_name' => trim($_POST['company_name'] ?? 'نظام المبيعات'),
                    'company_name_en' => trim($_POST['company_name_en'] ?? 'Sales System'),
                    'company_address' => trim($_POST['company_address'] ?? ''),
                    'company_phone' => trim($_POST['company_phone'] ?? ''),
                    'company_email' => trim($_POST['company_email'] ?? ''),
                    'company_website' => trim($_POST['company_website'] ?? ''),
                    'company_tax_number' => trim($_POST['company_tax_number'] ?? ''),
                    'company_commercial_register' => trim($_POST['company_commercial_register'] ?? ''),
                    'system_version' => trim($_POST['system_version'] ?? '2.0'),
                    'show_logo_on_invoices' => isset($_POST['show_logo_on_invoices']) ? '1' : '0',
                    'show_company_info_on_invoices' => isset($_POST['show_company_info_on_invoices']) ? '1' : '0'
                ];

                // معالجة رفع الشعار
                if (isset($_FILES['company_logo']) && $_FILES['company_logo']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = __DIR__ . '/uploads/';
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }

                    $file_extension = strtolower(pathinfo($_FILES['company_logo']['name'], PATHINFO_EXTENSION));
                    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];

                    if (in_array($file_extension, $allowed_extensions)) {
                        $new_filename = 'company_logo_' . time() . '.' . $file_extension;
                        $upload_path = $upload_dir . $new_filename;

                        if (move_uploaded_file($_FILES['company_logo']['tmp_name'], $upload_path)) {
                            // حذف الشعار القديم إذا وجد
                            $old_logo = getSetting('company_logo');
                            if ($old_logo && file_exists(__DIR__ . '/uploads/' . $old_logo)) {
                                unlink(__DIR__ . '/uploads/' . $old_logo);
                            }

                            $settings['company_logo'] = $new_filename;
                            error_log("Logo uploaded successfully: " . $new_filename);
                        } else {
                            error_log("Failed to move uploaded logo file");
                        }
                    } else {
                        error_log("Invalid logo file extension: " . $file_extension);
                    }
                } elseif (isset($_FILES['company_logo']) && $_FILES['company_logo']['error'] !== UPLOAD_ERR_NO_FILE) {
                    error_log("Logo upload error: " . $_FILES['company_logo']['error']);
                }

                // حفظ الإعدادات في قاعدة البيانات
                $success_count = 0;
                $error_count = 0;

                foreach ($settings as $key => $value) {
                    $stmt = $main_db->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
                    if (!$stmt) {
                        error_log("Failed to prepare statement for key: $key - " . $main_db->error);
                        $error_count++;
                        continue;
                    }

                    $stmt->bind_param("ss", $key, $value);
                    if ($stmt->execute()) {
                        $success_count++;
                        error_log("Successfully saved setting: $key = $value");
                    } else {
                        error_log("Failed to save setting: $key - " . $stmt->error);
                        $error_count++;
                    }
                    $stmt->close();
                }

                if ($success_count > 0) {
                    logActivity('company_info_updated', 'system_settings', null, null, null, "تم تحديث $success_count إعداد من معلومات الشركة");
                    $_SESSION['success'] = "تم تحديث معلومات الشركة بنجاح ($success_count إعداد)";

                    if ($error_count > 0) {
                        $_SESSION['warning'] = "تم حفظ معظم الإعدادات، لكن فشل في حفظ $error_count إعداد";
                    }
                } else {
                    $_SESSION['error'] = 'فشل في حفظ معلومات الشركة';
                }

            } catch (Exception $e) {
                error_log("Exception in update_company_info: " . $e->getMessage());
                $_SESSION['error'] = 'حدث خطأ أثناء حفظ معلومات الشركة: ' . $e->getMessage();
            }
            break;

        case 'backup_database':
            // إنشاء نسخة احتياطية
            $backup_file = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
            $backup_path = __DIR__ . '/backups/' . $backup_file;

            // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
            if (!is_dir(__DIR__ . '/backups')) {
                mkdir(__DIR__ . '/backups', 0755, true);
            }

            // تنفيذ النسخ الاحتياطي
            $command = "mysqldump -h" . MAIN_DB_HOST . " -u" . MAIN_DB_USER . " -p" . MAIN_DB_PASS . " " . MAIN_DB_NAME . " > " . $backup_path;
            exec($command, $output, $return_var);

            if ($return_var === 0) {
                logActivity('database_backup', 'system', null, null, null, 'تم إنشاء نسخة احتياطية: ' . $backup_file);
                $_SESSION['success'] = 'تم إنشاء النسخة الاحتياطية بنجاح';
            } else {
                $_SESSION['error'] = 'فشل في إنشاء النسخة الاحتياطية';
            }
            break;

        case 'clear_logs':
            // مسح السجلات القديمة
            $days = intval($_POST['days'] ?? 30);
            $stmt = $main_db->prepare("DELETE FROM activity_log WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
            $stmt->bind_param("i", $days);
            if ($stmt->execute()) {
                $deleted_rows = $stmt->affected_rows;
                logActivity('logs_cleared', 'activity_log', null, null, null, "تم حذف $deleted_rows سجل أقدم من $days يوم");
                $_SESSION['success'] = "تم حذف $deleted_rows سجل قديم";
            } else {
                $_SESSION['error'] = 'فشل في مسح السجلات';
            }
            $stmt->close();
            break;

        case 'optimize_database':
            // تحسين قاعدة البيانات
            $tables = ['users', 'admins', 'activity_log', 'sales', 'purchases', 'customers', 'products'];
            $optimized = 0;
            foreach ($tables as $table) {
                if ($main_db->query("OPTIMIZE TABLE $table")) {
                    $optimized++;
                }
            }
            logActivity('database_optimized', 'system', null, null, null, "تم تحسين $optimized جدول");
            $_SESSION['success'] = "تم تحسين $optimized جدول في قاعدة البيانات";
            break;
    }

    header("Location: admin_system.php");
    exit();
}

// جلب إحصائيات النظام
$system_stats = [];

// إحصائيات قاعدة البيانات
$db_size_query = "SELECT 
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS db_size_mb
    FROM information_schema.tables 
    WHERE table_schema = '" . MAIN_DB_NAME . "'";
$db_size_result = $main_db->query($db_size_query);
$system_stats['db_size'] = $db_size_result ? $db_size_result->fetch_assoc()['db_size_mb'] : 0;

// عدد السجلات في الجداول الرئيسية
$system_stats['users_count'] = $main_db->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
$system_stats['admins_count'] = $main_db->query("SELECT COUNT(*) as count FROM admins")->fetch_assoc()['count'];
$system_stats['logs_count'] = $main_db->query("SELECT COUNT(*) as count FROM activity_log")->fetch_assoc()['count'];

// إحصائيات النسخ الاحتياطية
$backup_dir = __DIR__ . '/backups';
$backup_files = [];
if (is_dir($backup_dir)) {
    $files = scandir($backup_dir);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $backup_files[] = [
                'name' => $file,
                'size' => filesize($backup_dir . '/' . $file),
                'date' => filemtime($backup_dir . '/' . $file)
            ];
        }
    }
    // ترتيب حسب التاريخ (الأحدث أولاً)
    usort($backup_files, function($a, $b) {
        return $b['date'] - $a['date'];
    });
}

require_once __DIR__ . '/includes/admin_header_new.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي -->
        <nav class="modern-sidebar d-none d-lg-block"><div class="sidebar-section"><div class="sidebar-section-title">الإدارة الرئيسية</div><a class="sidebar-nav-link" href="admin_dashboard.php"><i class="fas fa-tachometer-alt"></i><span>لوحة التحكم</span></a><a class="sidebar-nav-link" href="admin_users.php"><i class="fas fa-users"></i><span>إدارة المستخدمين</span></a><a class="sidebar-nav-link" href="admin_activity.php"><i class="fas fa-history"></i><span>سجل العمليات</span></a></div><div class="sidebar-section"><div class="sidebar-section-title">التقارير والإحصائيات</div><a class="sidebar-nav-link" href="admin_reports.php"><i class="fas fa-chart-bar"></i><span>التقارير الشاملة</span></a><a class="sidebar-nav-link" href="admin_financial.php"><i class="fas fa-file-invoice-dollar"></i><span>التقارير المالية</span></a></div><div class="sidebar-section"><div class="sidebar-section-title">إدارة النظام</div><a class="sidebar-nav-link" href="admin_error_logs.php"><i class="fas fa-exclamation-triangle"></i><span>سجل الأخطاء</span></a><a class="sidebar-nav-link active" href="admin_system.php"><i class="fas fa-cogs"></i><span>إعدادات النظام</span></a></div><div class="sidebar-section"><div class="sidebar-section-title">إدارة المديرين</div><a class="sidebar-nav-link" href="admin_manage_admins.php"><i class="fas fa-user-shield"></i><span>إدارة المديرين</span></a></div></nav>

        <!-- المحتوى الرئيسي -->
        <main class="col-lg-9 ms-auto px-4 py-3" style="margin-right: 300px; min-height: calc(100vh - 80px);">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-cogs me-2 text-primary"></i>إعدادات النظام
                </h1>
            </div>

            <?php displayMessages(); ?>

            <!-- إحصائيات النظام -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="modern-card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        حجم قاعدة البيانات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($system_stats['db_size'], 2); ?> MB
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-database fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="modern-card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        المستخدمين المسجلين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($system_stats['users_count']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="modern-card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        المديرين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($system_stats['admins_count']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="modern-card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        سجلات النشاط
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($system_stats['logs_count']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-list fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات معلومات الشركة -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-info">معلومات الشركة والبرنامج</h6>
                        </div>
                        <div class="modern-card-body">
                            <form method="POST" enctype="multipart/form-data">
                                <input type="hidden" name="action" value="update_company_info">

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="modern-form-label">اسم الشركة/البرنامج (عربي)</label>
                                            <input type="text" name="company_name" class="modern-form-control"
                                                   value="<?php echo getSetting('company_name', 'نظام المبيعات'); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label class="modern-form-label">اسم الشركة/البرنامج (إنجليزي)</label>
                                            <input type="text" name="company_name_en" class="modern-form-control"
                                                   value="<?php echo getSetting('company_name_en', 'Sales System'); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label class="modern-form-label">عنوان الشركة</label>
                                            <textarea name="company_address" class="modern-form-control" rows="3"><?php echo getSetting('company_address'); ?></textarea>
                                        </div>

                                        <div class="mb-3">
                                            <label class="modern-form-label">رقم الهاتف</label>
                                            <input type="text" name="company_phone" class="modern-form-control"
                                                   value="<?php echo getSetting('company_phone'); ?>">
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="modern-form-label">البريد الإلكتروني</label>
                                            <input type="email" name="company_email" class="modern-form-control"
                                                   value="<?php echo getSetting('company_email'); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label class="modern-form-label">الموقع الإلكتروني</label>
                                            <input name="company_website" class="modern-form-control"
                                                   value="<?php echo getSetting('company_website'); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label class="modern-form-label">الرقم الضريبي</label>
                                            <input type="text" name="company_tax_number" class="modern-form-control"
                                                   value="<?php echo getSetting('company_tax_number'); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label class="modern-form-label">رقم السجل التجاري</label>
                                            <input type="text" name="company_commercial_register" class="modern-form-control"
                                                   value="<?php echo getSetting('company_commercial_register'); ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="modern-form-label">إصدار النظام</label>
                                            <input type="text" name="system_version" class="modern-form-control"
                                                   value="<?php echo getSetting('system_version', '2.0'); ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label class="modern-form-label">شعار الشركة</label>
                                            <input type="file" name="company_logo" class="modern-form-control" accept="image/*">
                                            <?php $current_logo = getSetting('company_logo'); ?>
                                            <?php if ($current_logo): ?>
                                                <div class="mt-2">
                                                    <img src="uploads/<?php echo $current_logo; ?>" alt="شعار الشركة"
                                                         style="max-width: 100px; max-height: 100px;" class="img-thumbnail">
                                                    <small class="text-muted d-block">الشعار الحالي</small>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="show_logo_on_invoices"
                                                       <?php echo getSetting('show_logo_on_invoices', '1') == '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label">عرض الشعار في الفواتير</label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="show_company_info_on_invoices"
                                                       <?php echo getSetting('show_company_info_on_invoices', '1') == '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label">عرض معلومات الشركة في الفواتير</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" class="modern-btn modern-btn-info">
                                    <i class="fas fa-save me-1"></i>حفظ معلومات الشركة
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات المبيعات والمشتريات -->
            <div class="row mb-4">
                <!-- إعدادات المبيعات -->
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-success">إعدادات المبيعات</h6>
                        </div>
                        <div class="modern-card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_sales_settings">

                                <div class="mb-3">
                                    <label class="modern-form-label">معدل الضريبة الافتراضي (%)</label>
                                    <input type="number" name="default_tax_rate" class="modern-form-control"
                                           value="<?php echo getSetting('default_tax_rate', '15'); ?>"
                                           step="0.01" min="0" max="100">
                                </div>

                                <div class="mb-3">
                                    <label class="modern-form-label">بادئة رقم الفاتورة</label>
                                    <input type="text" name="invoice_prefix" class="modern-form-control"
                                           value="<?php echo getSetting('invoice_prefix', 'INV'); ?>">
                                </div>

                                <div class="mb-3">
                                    <label class="modern-form-label">رمز العملة</label>
                                    <input type="text" name="currency_symbol" class="modern-form-control"
                                           value="<?php echo getSetting('currency_symbol', 'ريال'); ?>">
                                </div>

                                <div class="mb-3">
                                    <label class="modern-form-label">عدد الخانات العشرية</label>
                                    <select name="decimal_places" class="form-select">
                                        <option value="0" <?php echo getSetting('decimal_places', '2') == '0' ? 'selected' : ''; ?>>0</option>
                                        <option value="1" <?php echo getSetting('decimal_places', '2') == '1' ? 'selected' : ''; ?>>1</option>
                                        <option value="2" <?php echo getSetting('decimal_places', '2') == '2' ? 'selected' : ''; ?>>2</option>
                                        <option value="3" <?php echo getSetting('decimal_places', '2') == '3' ? 'selected' : ''; ?>>3</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="modern-form-label">طريقة الدفع الافتراضية</label>
                                    <select name="default_payment_method" class="form-select">
                                        <option value="cash" <?php echo getSetting('default_payment_method', 'cash') == 'cash' ? 'selected' : ''; ?>>نقدي</option>
                                        <option value="card" <?php echo getSetting('default_payment_method', 'cash') == 'card' ? 'selected' : ''; ?>>بطاقة</option>
                                        <option value="bank_transfer" <?php echo getSetting('default_payment_method', 'cash') == 'bank_transfer' ? 'selected' : ''; ?>>تحويل بنكي</option>
                                        <option value="check" <?php echo getSetting('default_payment_method', 'cash') == 'check' ? 'selected' : ''; ?>>شيك</option>
                                    </select>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="auto_invoice_number"
                                           <?php echo getSetting('auto_invoice_number', '1') == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label">ترقيم تلقائي للفواتير</label>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="require_customer"
                                           <?php echo getSetting('require_customer', '0') == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label">إجبار اختيار عميل</label>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="allow_negative_stock"
                                           <?php echo getSetting('allow_negative_stock', '0') == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label">السماح بالمخزون السالب</label>
                                </div>

                                <button type="submit" class="modern-btn modern-btn-success">
                                    <i class="fas fa-save me-1"></i>حفظ إعدادات المبيعات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- إعدادات المشتريات -->
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-danger">إعدادات المشتريات</h6>
                        </div>
                        <div class="modern-card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_purchase_settings">

                                <div class="mb-3">
                                    <label class="modern-form-label">بادئة رقم فاتورة الشراء</label>
                                    <input type="text" name="purchase_invoice_prefix" class="modern-form-control"
                                           value="<?php echo getSetting('purchase_invoice_prefix', 'PUR'); ?>">
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="auto_purchase_number"
                                           <?php echo getSetting('auto_purchase_number', '1') == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label">ترقيم تلقائي لفواتير الشراء</label>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="require_supplier"
                                           <?php echo getSetting('require_supplier', '0') == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label">إجبار اختيار مورد</label>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="auto_update_stock"
                                           <?php echo getSetting('auto_update_stock', '1') == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label">تحديث المخزون تلقائياً</label>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="purchase_approval_required"
                                           <?php echo getSetting('purchase_approval_required', '0') == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label">يتطلب موافقة على المشتريات</label>
                                </div>

                                <button type="submit" class="modern-btn modern-btn-danger">
                                    <i class="fas fa-save me-1"></i>حفظ إعدادات المشتريات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- أدوات النظام -->
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">أدوات النظام</h6>
                        </div>
                        <div class="modern-card-body">
                            <!-- النسخ الاحتياطي -->
                            <div class="mb-4">
                                <h6><i class="fas fa-download me-2 text-success"></i>النسخ الاحتياطي</h6>
                                <p class="text-muted">إنشاء نسخة احتياطية من قاعدة البيانات الرئيسية</p>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="backup_database">
                                    <button type="submit" class="modern-btn modern-btn-success btn-sm" onclick="return confirm('هل تريد إنشاء نسخة احتياطية؟')">
                                        <i class="fas fa-download me-1"></i>إنشاء نسخة احتياطية
                                    </button>
                                </form>
                            </div>

                            <hr>

                            <!-- مسح السجلات -->
                            <div class="mb-4">
                                <h6><i class="fas fa-trash me-2 text-warning"></i>مسح السجلات القديمة</h6>
                                <p class="text-muted">حذف سجلات النشاط الأقدم من عدد الأيام المحدد</p>
                                <form method="POST" class="d-flex align-items-end gap-2">
                                    <input type="hidden" name="action" value="clear_logs">
                                    <div>
                                        <label class="modern-form-label">عدد الأيام</label>
                                        <input type="number" name="days" class="form-control form-control-sm" value="30" min="1" max="365" style="width: 100px;">
                                    </div>
                                    <button type="submit" class="modern-btn modern-btn-warning btn-sm" onclick="return confirm('هل تريد حذف السجلات القديمة؟')">
                                        <i class="fas fa-trash me-1"></i>مسح السجلات
                                    </button>
                                </form>
                            </div>

                            <hr>

                            <!-- تحسين قاعدة البيانات -->
                            <div class="mb-4">
                                <h6><i class="fas fa-tools me-2 text-info"></i>تحسين قاعدة البيانات</h6>
                                <p class="text-muted">تحسين جداول قاعدة البيانات لتحسين الأداء</p>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="optimize_database">
                                    <button type="submit" class="modern-btn modern-btn-info btn-sm" onclick="return confirm('هل تريد تحسين قاعدة البيانات؟')">
                                        <i class="fas fa-tools me-1"></i>تحسين قاعدة البيانات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- النسخ الاحتياطية -->
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">النسخ الاحتياطية</h6>
                        </div>
                        <div class="modern-card-body">
                            <?php if (!empty($backup_files)): ?>
                            <div class="modern-table table-responsive">
                                <table class="modern-table table table-sm">
                                    <thead>
                                        <tr>
                                            <th>اسم الملف</th>
                                            <th>الحجم</th>
                                            <th><?php echo t("date"); ?></th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach (array_slice($backup_files, 0, 10) as $backup): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($backup['name']); ?></td>
                                            <td><?php echo number_format($backup['size'] / 1024, 2); ?> KB</td>
                                            <td><?php echo date('Y-m-d H:i', $backup['date']); ?></td>
                                            <td>
                                                <a href="backups/<?php echo urlencode($backup['name']); ?>" 
                                                   class="modern-btn modern-btn-sm btn-outline-primary" download>
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-folder-open fa-3x mb-3"></i>
                                <p>لا توجد نسخ احتياطية</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات النظام</h6>
                </div>
                <div class="modern-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="modern-table table table-sm">
                                <tr>
                                    <th>إصدار PHP:</th>
                                    <td><?php echo phpversion(); ?></td>
                                </tr>
                                <tr>
                                    <th>إصدار MySQL:</th>
                                    <td><?php echo $main_db->server_info; ?></td>
                                </tr>
                                <tr>
                                    <th>نظام التشغيل:</th>
                                    <td><?php echo php_uname('s') . ' ' . php_uname('r'); ?></td>
                                </tr>
                                <tr>
                                    <th>الذاكرة المحددة:</th>
                                    <td><?php echo ini_get('memory_limit'); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="modern-table table table-sm">
                                <tr>
                                    <th>حد رفع الملفات:</th>
                                    <td><?php echo ini_get('upload_max_filesize'); ?></td>
                                </tr>
                                <tr>
                                    <th>حد POST:</th>
                                    <td><?php echo ini_get('post_max_size'); ?></td>
                                </tr>
                                <tr>
                                    <th>مهلة التنفيذ:</th>
                                    <td><?php echo ini_get('max_execution_time'); ?> ثانية</td>
                                </tr>
                                <tr>
                                    <th>المنطقة الزمنية:</th>
                                    <td><?php echo date_default_timezone_get(); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </main>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.sidebar {
    min-height: 100vh;
}
.nav-link.active {
    background-color: #495057 !important;
}
</style>

<?php require_once __DIR__ . '/includes/admin_footer_new.php'; ?>
