<?php
/**
 * صفحة إدارة المستخدمين
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// التحقق من الصلاحيات
if (!hasAdminPermission('manage_users')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    // معالجة العمليات
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        $user_id = intval($_POST['user_id'] ?? 0);

        switch ($action) {
            case 'toggle_status':
                if ($user_id > 0) {
                    // تصحيح: استخدام status بدلاً من is_active
                    $stmt = $db->prepare("UPDATE users SET status = CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END WHERE id = ?");
                    if (!$stmt) {
                        ErrorHandler::logDatabaseError("UPDATE users SET status", $db->error);
                        throw new Exception("خطأ في إعداد الاستعلام");
                    }

                    $stmt->bind_param("i", $user_id);
                    if ($stmt->execute()) {
                        logActivity('user_status_changed', 'users', $user_id, null, null, 'تغيير حالة المستخدم');
                        showSuccessMessage('تم تغيير حالة المستخدم بنجاح');
                    } else {
                        ErrorHandler::logDatabaseError("UPDATE users SET status", $stmt->error);
                        showErrorMessage('فشل في تغيير حالة المستخدم');
                    }
                    $stmt->close();
                }
                break;

            case 'reset_password':
                if ($user_id > 0) {
                    $new_password = 'user123'; // كلمة مرور افتراضية
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

                    $stmt = $db->prepare("UPDATE users SET password = ? WHERE id = ?");
                    if (!$stmt) {
                        ErrorHandler::logDatabaseError("UPDATE users SET password", $db->error);
                        throw new Exception("خطأ في إعداد الاستعلام");
                    }

                    $stmt->bind_param("si", $hashed_password, $user_id);

                    if ($stmt->execute()) {
                        logActivity('password_reset', 'users', $user_id, null, null, "تم إعادة تعيين كلمة مرور المستخدم ID: $user_id");
                        showSuccessMessage("تم إعادة تعيين كلمة المرور بنجاح. كلمة المرور الجديدة: $new_password");
                    } else {
                        ErrorHandler::logDatabaseError("UPDATE users SET password", $stmt->error);
                        showErrorMessage('فشل في إعادة تعيين كلمة المرور');
                    }
                    $stmt->close();
                }
                break;

            case 'delete_user':
                if ($user_id > 0) {
                    // بدء معاملة
                    $db->begin_transaction();

                    try {
                        // حذف بيانات المستخدم من الجداول المختلفة
                        $tables_to_clean = ['sales', 'purchases', 'customers', 'activity_log'];

                        foreach ($tables_to_clean as $table) {
                            $clean_stmt = $db->prepare("DELETE FROM $table WHERE user_id = ?");
                            if ($clean_stmt) {
                                $clean_stmt->bind_param("i", $user_id);
                                $clean_stmt->execute();
                                $clean_stmt->close();
                            }
                        }

                        // حذف المستخدم من الجدول الرئيسي
                        $stmt = $db->prepare("DELETE FROM users WHERE id = ?");
                        if (!$stmt) {
                            throw new Exception("خطأ في إعداد استعلام الحذف");
                        }

                        $stmt->bind_param("i", $user_id);
                        if (!$stmt->execute()) {
                            throw new Exception("فشل في حذف المستخدم");
                        }
                        $stmt->close();

                        // تأكيد المعاملة
                        $db->commit();

                        logActivity('user_deleted', 'users', $user_id, null, null, 'حذف المستخدم وجميع بياناته');
                        showSuccessMessage('تم حذف المستخدم وجميع بياناته بنجاح');

                    } catch (Exception $e) {
                        $db->rollback();
                        ErrorHandler::logError('ERROR', 'User deletion failed: ' . $e->getMessage(), __FILE__, __LINE__, ['user_id' => $user_id]);
                        showErrorMessage('فشل في حذف المستخدم: ' . $e->getMessage());
                    }
                }
                break;

            case 'bulk_action':
                $selected_users = $_POST['selected_users'] ?? [];
                $bulk_action = $_POST['bulk_action'] ?? '';

                if (!empty($selected_users) && !empty($bulk_action)) {
                    $success_count = 0;
                    $error_count = 0;

                    foreach ($selected_users as $uid) {
                        $uid = intval($uid);
                        if ($uid <= 0) continue;

                        try {
                            switch ($bulk_action) {
                                case 'activate':
                                    $stmt = $db->prepare("UPDATE users SET status = 'active' WHERE id = ?");
                                    break;
                                case 'deactivate':
                                    $stmt = $db->prepare("UPDATE users SET status = 'inactive' WHERE id = ?");
                                    break;
                                default:
                                    continue 2;
                            }

                            if ($stmt) {
                                $stmt->bind_param("i", $uid);
                                if ($stmt->execute()) {
                                    $success_count++;
                                    logActivity("bulk_$bulk_action", 'users', $uid, null, null, "عملية جماعية: $bulk_action");
                                } else {
                                    $error_count++;
                                }
                                $stmt->close();
                            }
                        } catch (Exception $e) {
                            $error_count++;
                            ErrorHandler::logError('ERROR', "Bulk action failed for user $uid: " . $e->getMessage(), __FILE__, __LINE__);
                        }
                    }

                    if ($success_count > 0) {
                        showSuccessMessage("تم تنفيذ العملية على $success_count مستخدم بنجاح");
                    }
                    if ($error_count > 0) {
                        showWarningMessage("فشل تنفيذ العملية على $error_count مستخدم");
                    }
                }
                break;
        }

        header("Location: admin_users.php");
        exit();
    }

} catch (Exception $e) {
    ErrorHandler::logError('ERROR', 'Admin users page error: ' . $e->getMessage(), __FILE__, __LINE__);
    showErrorMessage('حدث خطأ في النظام: ' . $e->getMessage());
}

// جلب المستخدمين مع الإحصائيات والترقيم
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_conditions[] = "(username LIKE ? OR full_name LIKE ? OR email LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'sss';
}

if ($status_filter !== '') {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter === '1' ? 'active' : 'inactive';
    $param_types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // استعلام العد الإجمالي
    $count_query = "SELECT COUNT(*) as total FROM users $where_clause";
    if (!empty($params)) {
        $count_stmt = $db->prepare($count_query);
        if (!$count_stmt) {
            ErrorHandler::logDatabaseError($count_query, $db->error);
            throw new Exception("خطأ في إعداد استعلام العد");
        }
        $count_stmt->bind_param($param_types, ...$params);
        $count_stmt->execute();
        $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
        $count_stmt->close();
    } else {
        $count_result = $db->query($count_query);
        if (!$count_result) {
            ErrorHandler::logDatabaseError($count_query, $db->error);
            throw new Exception("خطأ في تنفيذ استعلام العد");
        }
        $total_records = $count_result->fetch_assoc()['total'];
    }

    $total_pages = ceil($total_records / $per_page);

    // استعلام البيانات مع الترقيم (تصحيح اسم العمود)
    $query = "SELECT id, username, full_name, email, phone, status, last_login, created_at FROM users $where_clause ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";

    if (!empty($params)) {
        $stmt = $db->prepare($query);
        if (!$stmt) {
            ErrorHandler::logDatabaseError($query, $db->error);
            throw new Exception("خطأ في إعداد استعلام البيانات");
        }
        $stmt->bind_param($param_types, ...$params);
        $stmt->execute();
        $users_result = $stmt->get_result();
    } else {
        $users_result = $db->query($query);
        if (!$users_result) {
            ErrorHandler::logDatabaseError($query, $db->error);
            throw new Exception("خطأ في تنفيذ استعلام البيانات");
        }
    }

require_once __DIR__ . '/includes/admin_header_new.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي المتطور -->
        <nav class="modern-sidebar d-none d-lg-block">
            <div class="sidebar-section">
                <div class="sidebar-section-title">الإدارة الرئيسية</div>
                <a class="sidebar-nav-link" href="admin_dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
                <a class="sidebar-nav-link active" href="admin_users.php">
                    <i class="fas fa-users"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                <a class="sidebar-nav-link" href="admin_activity.php">
                    <i class="fas fa-history"></i>
                    <span>سجل العمليات</span>
                </a>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-title">التقارير والإحصائيات</div>
                <a class="sidebar-nav-link" href="admin_reports.php">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير الشاملة</span>
                </a>
                <a class="sidebar-nav-link" href="admin_financial.php">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <span>التقارير المالية</span>
                </a>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة النظام</div>
                <a class="sidebar-nav-link" href="admin_error_logs.php">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>سجل الأخطاء</span>
                </a>
                <a class="sidebar-nav-link" href="admin_system.php">
                    <i class="fas fa-cogs"></i>
                    <span>إعدادات النظام</span>
                </a>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة المديرين</div>
                <a class="sidebar-nav-link" href="admin_manage_admins.php">
                    <i class="fas fa-user-shield"></i>
                    <span>إدارة المديرين</span>
                </a>
            </div>
        </nav>

        <!-- المحتوى الرئيسي المتطور -->
        <main class="col-lg-9 ms-auto px-4 py-3" style="margin-right: 300px; min-height: calc(100vh - 80px);">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4 fade-in">
                <div>
                    <h1 class="h2 mb-2 fw-bold gradient-text">
                        <i class="fas fa-users me-3"></i>
                        إدارة المستخدمين
                    </h1>
                    <p class="text-muted mb-0">إدارة وتنظيم حسابات المستخدمين في النظام</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="modern-btn modern-btn-success" onclick="exportData('excel', 'admin_export.php?type=users')">
                        <i class="fas fa-download"></i>
                        <span>تصدير Excel</span>
                    </button>
                    <button type="button" class="modern-btn modern-btn-outline" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i>
                        <span>تحديث</span>
                    </button>
                </div>
            </div>

            <!-- فلاتر البحث المتطورة -->
            <div class="modern-card hover-lift fade-in mb-4">
                <div class="modern-card-header">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث والتصفية
                    </h5>
                </div>
                <div class="modern-card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="modern-form-label">البحث</label>
                            <input type="text" class="modern-form-control" id="search" name="search"
                                   value="<?php echo htmlspecialchars($search); ?>"
                                   placeholder="اسم المستخدم، الاسم الكامل، أو البريد الإلكتروني">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="modern-form-label">الحالة</label>
                            <select class="modern-form-control" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="1" <?php echo $status_filter === '1' ? 'selected' : ''; ?>>نشط</option>
                                <option value="0" <?php echo $status_filter === '0' ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-5 d-flex align-items-end gap-2">
                            <button type="submit" class="modern-btn modern-btn-primary">
                                <i class="fas fa-search"></i>
                                <span>بحث</span>
                            </button>
                            <a href="admin_users.php" class="modern-btn modern-btn-outline">
                                <i class="fas fa-redo"></i>
                                <span>إعادة تعيين</span>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- العمليات الجماعية المتطورة -->
            <div class="modern-card hover-lift fade-in mb-4" style="animation-delay: 0.1s;">
                <div class="modern-card-header">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-tasks me-2"></i>
                        العمليات الجماعية
                    </h5>
                </div>
                <div class="modern-card-body">
                    <form id="bulk_form" method="POST">
                        <input type="hidden" name="action" value="bulk_action">
                        <div class="row align-items-center g-3">
                            <div class="col-md-4">
                                <label class="modern-form-label">اختر العملية</label>
                                <select class="modern-form-control" name="bulk_action" id="bulk_action">
                                    <option value="">اختر عملية جماعية</option>
                                    <option value="activate">تفعيل المحدد</option>
                                    <option value="deactivate">إلغاء تفعيل المحدد</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="button" class="modern-btn modern-btn-primary" onclick="bulkAction()">
                                    <i class="fas fa-play"></i>
                                    <span>تنفيذ</span>
                                </button>
                            </div>
                            <div class="col-md-5 d-flex align-items-end justify-content-end gap-2">
                                <button type="button" class="modern-btn modern-btn-success" onclick="exportUsers()">
                                    <i class="fas fa-file-excel"></i>
                                    <span>تصدير Excel</span>
                                </button>
                                <button type="button" class="modern-btn modern-btn-secondary" onclick="printUsers()">
                                    <i class="fas fa-print"></i>
                                    <span>طباعة</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول المستخدمين المتطور -->
            <div class="modern-card hover-lift fade-in" style="animation-delay: 0.2s;">
                <div class="modern-card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-users me-2"></i>قائمة المستخدمين
                        </h5>
                        <div class="d-flex align-items-center gap-3">
                            <span class="modern-badge modern-badge-primary">إجمالي: <?php echo number_format($total_records); ?></span>
                            <span class="modern-badge modern-badge-success">نشط: <?php echo number_format($users_stats['active_users'] ?? 0); ?></span>
                            <span class="modern-badge modern-badge-warning">غير نشط: <?php echo number_format(($users_stats['total_users'] ?? 0) - ($users_stats['active_users'] ?? 0)); ?></span>
                        </div>
                    </div>
                </div>
                <div class="modern-card-body p-0">
                    <div class="table-responsive">
                        <table class="modern-table table mb-0">
                                <tr>
                                    <th style="width: 40px;">
                                        <input type="checkbox" id="select_all" onchange="toggleAllUsers()">
                                    </th>
                                    <th style="width: 60px;">ID</th>
                                    <th>المستخدم</th>
                                    <th>معلومات الاتصال</th>
                                    <th style="width: 100px;"><?php echo t("status"); ?></th>
                                    <th style="width: 140px;">آخر نشاط</th>
                                    <th style="width: 100px;">التسجيل</th>
                                    <th style="width: 200px;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($users_result && $users_result->num_rows > 0): ?>
                                    <?php while ($user = $users_result->fetch_assoc()): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="selected_users[]" value="<?php echo $user['id']; ?>">
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo $user['id']; ?></span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-initial bg-primary rounded-circle">
                                                        <?php echo strtoupper(substr($user['full_name'] ?? $user['username'], 0, 1)); ?>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($user['full_name'] ?? 'غير محدد'); ?></div>
                                                    <small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <?php if (!empty($user['email'])): ?>
                                                <div class="mb-1">
                                                    <i class="fas fa-envelope text-muted me-1"></i>
                                                    <small><?php echo htmlspecialchars($user['email']); ?></small>
                                                </div>
                                                <?php endif; ?>
                                                <?php if (!empty($user['phone'])): ?>
                                                <div>
                                                    <i class="fas fa-phone text-muted me-1"></i>
                                                    <small><?php echo htmlspecialchars($user['phone']); ?></small>
                                                </div>
                                                <?php endif; ?>
                                                <?php if (empty($user['email']) && empty($user['phone'])): ?>
                                                <small class="text-muted">لا توجد معلومات اتصال</small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo $user['status'] === 'active' ? 'bg-success' : 'bg-danger'; ?>">
                                                <i class="fas <?php echo $user['status'] === 'active' ? 'fa-check' : 'fa-times'; ?> me-1"></i>
                                                <?php echo $user['status'] === 'active' ? t("active") : t("inactive"); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($user['last_login']): ?>
                                                <div class="text-success">
                                                    <i class="fas fa-clock me-1"></i>
                                                    <small><?php echo date('Y-m-d', strtotime($user['last_login'])); ?></small>
                                                </div>
                                                <small class="text-muted"><?php echo date('H:i', strtotime($user['last_login'])); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">
                                                    <i class="fas fa-minus-circle me-1"></i>
                                                    <small>لم يسجل دخول</small>
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo date('Y-m-d', strtotime($user['created_at'])); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="admin_user_details.php?id=<?php echo $user['id']; ?>"
                                                   class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>

                                                <a href="admin_financial.php?user_id=<?php echo $user['id']; ?>"
                                                   class="btn btn-sm btn-outline-primary" title="التقرير المالي">
                                                    <i class="fas fa-chart-line"></i>
                                                </a>

                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="toggle_status">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-outline-<?php echo $user['status'] === 'active' ? 'warning' : 'success'; ?>"
                                                            title="<?php echo $user['status'] === 'active' ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                        <i class="fas <?php echo $user['status'] === 'active' ? 'fa-ban' : 'fa-check'; ?>"></i>
                                                    </button>
                                                </form>

                                                <button type="button" class="btn btn-sm btn-outline-secondary"
                                                        onclick="resetUserPassword(<?php echo $user['id']; ?>)"
                                                        title="إعادة تعيين كلمة المرور">
                                                    <i class="fas fa-key"></i>
                                                </button>

                                                <form method="POST" style="display: inline;"
                                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ سيتم حذف جميع بياناته نهائياً!')">
                                                    <input type="hidden" name="action" value="delete_user">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title=t("delete")>
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-users fa-3x mb-3"></i>
                                                <h5>لا توجد مستخدمين</h5>
                                                <p>لم يتم العثور على مستخدمين مطابقين لمعايير البحث</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- التنقل بين الصفحات -->
                    <?php if ($total_pages > 1): ?>
                    <?php
                    // بناء معاملات الاستعلام بدون معامل page
                    $query_params = $_GET;
                    unset($query_params['page']);
                    $query_string = !empty($query_params) ? '&' . http_build_query($query_params) : '';
                    ?>
                    <nav aria-label="تنقل الصفحات">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page-1; ?><?php echo $query_string; ?>">السابق</a>
                            </li>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo $query_string; ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page+1; ?><?php echo $query_string; ?>">التالي</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- CSS مخصص -->
<style>
.avatar-sm {
    width: 32px;
    height: 32px;
}

.avatar-initial {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 12px;
    color: white;
}

.table th {
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white;
    border-bottom: none;
    padding: 12px 8px;
}

.table td {
    padding: 12px 8px;
    border-bottom: 1px solid var(--admin-border-light);
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: var(--admin-light);
}

.btn-group .btn {
    margin: 0 1px;
}

.badge {
    font-size: 10px;
    font-weight: 600;
    padding: 4px 8px;
}

.card-header {
    background: var(--admin-bg-header);
    border-bottom: 1px solid var(--admin-border-color);
    font-weight: 600;
}

/* تحسين الوضع الداكن */
[data-theme="dark"] .table th {
    background: var(--admin-slate);
    color: white;
}

[data-theme="dark"] .table td {
    border-bottom-color: var(--admin-border-color);
}

[data-theme="dark"] .table tbody tr:hover {
    background-color: var(--admin-border-light);
}

[data-theme="dark"] .avatar-initial {
    background: var(--admin-gradient-primary);
}
</style>

<script>
// إعادة تعيين كلمة مرور المستخدم
function resetUserPassword(userId) {
    Swal.fire({
        title: 'إعادة تعيين كلمة المرور',
        text: 'هل تريد إعادة تعيين كلمة مرور هذا المستخدم؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، أعد التعيين',
        cancelButtonText: t("cancel")
    }).then((result) => {
        if (result.isConfirmed) {
            // إنشاء نموذج مخفي لإرسال الطلب
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'reset_password';

            const userIdInput = document.createElement('input');
            userIdInput.type = 'hidden';
            userIdInput.name = 'user_id';
            userIdInput.value = userId;

            form.appendChild(actionInput);
            form.appendChild(userIdInput);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

// تصدير بيانات المستخدمين
function exportUsers() {
    const selectedUsers = document.querySelectorAll('input[name="selected_users[]"]:checked');
    let url = 'admin_export.php?type=users';

    if (selectedUsers.length > 0) {
        const userIds = Array.from(selectedUsers).map(cb => cb.value);
        url += '&user_ids=' + userIds.join(',');
    }

    window.open(url, '_blank');
}

// طباعة قائمة المستخدمين
function printUsers() {
    window.print();
}

// العمليات الجماعية
function bulkAction() {
    const checkboxes = document.querySelectorAll('input[name="selected_users[]"]:checked');
    const action = document.getElementById('bulk_action').value;

    if (checkboxes.length === 0) {
        Swal.fire({
            title: 'تحذير',
            text: 'يرجى اختيار مستخدم واحد على الأقل',
            icon: 'warning'
        });
        return;
    }

    if (!action) {
        Swal.fire({
            title: 'تحذير',
            text: 'يرجى اختيار إجراء',
            icon: 'warning'
        });
        return;
    }

    let title = '';
    let text = '';
    let icon = 'question';

    switch(action) {
        case 'activate':
            title = 'تفعيل المستخدمين';
            text = `هل تريد تفعيل ${checkboxes.length} مستخدم محدد؟`;
            break;
        case 'deactivate':
            title = 'إلغاء تفعيل المستخدمين';
            text = `هل تريد إلغاء تفعيل ${checkboxes.length} مستخدم محدد؟`;
            icon = 'warning';
            break;
    }

    Swal.fire({
        title: title,
        text: text,
        icon: icon,
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، تنفيذ',
        cancelButtonText: t("cancel")
    }).then((result) => {
        if (result.isConfirmed) {
            document.getElementById('bulk_form').submit();
        }
    });
}

// تحديد/إلغاء تحديد جميع المستخدمين
function toggleAllUsers() {
    const selectAll = document.getElementById('select_all');
    const checkboxes = document.querySelectorAll('input[name="selected_users[]"]');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateBulkActionVisibility();
}

// تحديث رؤية العمليات الجماعية
function updateBulkActionVisibility() {
    const checkboxes = document.querySelectorAll('input[name="selected_users[]"]:checked');
    const bulkCard = document.querySelector('.card:has(#bulk_form)');

    if (checkboxes.length > 0) {
        bulkCard.style.display = 'block';
    } else {
        bulkCard.style.display = 'none';
    }
}

// إضافة مستمعين للأحداث
document.addEventListener('DOMContentLoaded', function() {
    // إخفاء العمليات الجماعية في البداية
    updateBulkActionVisibility();

    // إضافة مستمعين لصناديق الاختيار
    document.querySelectorAll('input[name="selected_users[]"]').forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionVisibility);
    });

    // تحديث عدد المستخدمين المحددين
    document.querySelectorAll('input[name="selected_users[]"]').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const selected = document.querySelectorAll('input[name="selected_users[]"]:checked').length;
            const selectAllCheckbox = document.getElementById('select_all');
            const totalCheckboxes = document.querySelectorAll('input[name="selected_users[]"]').length;

            if (selected === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (selected === totalCheckboxes) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
            }
        });
    });
});

// تحديث تلقائي للصفحة كل 5 دقائق
setInterval(function() {
    // تحديث فقط إذا لم يكن هناك مستخدمين محددين
    const selected = document.querySelectorAll('input[name="selected_users[]"]:checked').length;
    if (selected === 0) {
        location.reload();
    }
}, 300000); // 5 دقائق
</script>

<!-- تضمين SweetAlert2 للتنبيهات المحسنة -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<?php require_once __DIR__ . '/includes/admin_footer_new.php'; ?>
