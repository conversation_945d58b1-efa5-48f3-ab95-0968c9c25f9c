<?php
/**
 * ملف اختبار سجل الأخطاء
 */

require_once __DIR__ . '/config/init.php';

// محاكاة تسجيل دخول المدير للاختبار
$_SESSION['admin_id'] = 1;
$_SESSION['admin_username'] = 'test_admin';
$_SESSION['admin_permissions'] = ['view_system_logs' => true];

// اختبار تسجيل أخطاء مختلفة
ErrorHandler::logError('ERROR', 'هذا خطأ تجريبي للاختبار', __FILE__, __LINE__);
ErrorHandler::logError('WARNING', 'هذا تحذير تجريبي للاختبار', __FILE__, __LINE__);
ErrorHandler::logError('CRITICAL', 'هذا خطأ حرج تجريبي للاختبار', __FILE__, __LINE__);
ErrorHandler::logDatabaseError('SELECT * FROM test_table', 'Table does not exist');
ErrorHandler::logAuthError('محاولة دخول غير صحيحة', 'test_user');

// اختبار جلب السجلات
if (isset($_GET['action']) && $_GET['action'] === 'get_logs') {
    header('Content-Type: application/json');
    
    $date = $_GET['date'] ?? date('Y-m-d');
    $level = $_GET['level'] ?? null;
    $limit = intval($_GET['limit'] ?? 100);
    
    try {
        $logs = ErrorHandler::getLogs($date, $level, $limit);
        echo json_encode(['success' => true, 'logs' => $logs, 'count' => count($logs)]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'حدث خطأ في جلب السجلات: ' . $e->getMessage()]);
    }
    exit();
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سجل الأخطاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>اختبار سجل الأخطاء</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>إضافة أخطاء تجريبية</h3>
                <p>تم إضافة أخطاء تجريبية مختلفة للاختبار.</p>
                
                <h3>اختبار جلب السجلات</h3>
                <button class="btn btn-primary" onclick="testGetLogs()">اختبار جلب السجلات</button>
                
                <div id="testResults" class="mt-3"></div>
            </div>
            
            <div class="col-md-6">
                <h3>الروابط</h3>
                <ul>
                    <li><a href="admin_error_logs.php" target="_blank">صفحة سجل الأخطاء الأصلية</a></li>
                    <li><a href="test_error_logs.php?action=get_logs" target="_blank">اختبار API مباشرة</a></li>
                </ul>
            </div>
        </div>
    </div>

    <script>
    function testGetLogs() {
        const resultsDiv = document.getElementById('testResults');
        resultsDiv.innerHTML = '<div class="alert alert-info">جاري الاختبار...</div>';
        
        fetch('test_error_logs.php?action=get_logs&date=<?php echo date('Y-m-d'); ?>&limit=10')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h5>نجح الاختبار!</h5>
                            <p>تم جلب ${data.count} سجل بنجاح.</p>
                            <details>
                                <summary>عرض السجلات</summary>
                                <pre>${JSON.stringify(data.logs, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h5>فشل الاختبار!</h5>
                            <p>خطأ: ${data.error}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>خطأ في الاتصال!</h5>
                        <p>${error.message}</p>
                    </div>
                `;
            });
    }
    </script>
</body>
</html>
