<?php
/**
 * اختبار ثبات القائمة الجانبية
 */

session_start();

// محاكاة تسجيل دخول المدير
$_SESSION['admin_id'] = 1;
$_SESSION['admin_username'] = 'admin';
$_SESSION['admin_is_super'] = true;
$_SESSION['admin_permissions'] = [
    'view_system_logs' => true,
    'manage_users' => true,
    'view_reports' => true,
    'manage_system' => true,
    'manage_admins' => true
];

require_once __DIR__ . '/includes/admin_header.php';
?>

<?php include __DIR__ . '/includes/admin_navbar.php'; ?>

<div class="container-fluid">
    <div class="row">
        <?php include __DIR__ . '/includes/admin_sidebar.php'; ?>

        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-check-circle me-2 text-success"></i>
                    اختبار ثبات القائمة الجانبية
                </h1>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-list-check me-2"></i>
                                اختبار العناصر
                            </h5>
                        </div>
                        <div class="card-body">
                            <h6>تعليمات الاختبار:</h6>
                            <ol>
                                <li>تأكد من ظهور جميع عناصر القائمة الجانبية</li>
                                <li>اضغط على زر تبديل الوضع الداكن/الفاتح</li>
                                <li>تأكد من عدم اختفاء أي عناصر</li>
                                <li>تأكد من ثبات الألوان والتنسيق</li>
                                <li>جرب التنقل بين الصفحات المختلفة</li>
                            </ol>

                            <div class="alert alert-info mt-3">
                                <h6><i class="fas fa-info-circle me-2"></i>العناصر المتوقعة في القائمة الجانبية:</h6>
                                <ul class="mb-0">
                                    <li><strong>الإدارة الرئيسية:</strong>
                                        <ul>
                                            <li>لوحة التحكم</li>
                                            <li>إدارة المستخدمين</li>
                                            <li>سجل العمليات</li>
                                        </ul>
                                    </li>
                                    <li><strong>التقارير والإحصائيات:</strong>
                                        <ul>
                                            <li>التقارير الشاملة</li>
                                            <li>التقارير المالية</li>
                                        </ul>
                                    </li>
                                    <li><strong>إدارة النظام:</strong>
                                        <ul>
                                            <li>سجل الأخطاء</li>
                                            <li>إعدادات النظام</li>
                                        </ul>
                                    </li>
                                    <li><strong>أدوات التشخيص:</strong>
                                        <ul>
                                            <li>حالة النظام</li>
                                            <li>حالة الترجمة</li>
                                        </ul>
                                    </li>
                                    <li><strong>إدارة المديرين:</strong>
                                        <ul>
                                            <li>إدارة المديرين</li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>

                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">✅ اختبار الوضع الفاتح</h6>
                                        </div>
                                        <div class="card-body">
                                            <p>تأكد من:</p>
                                            <ul>
                                                <li>ظهور جميع العناصر</li>
                                                <li>وضوح النصوص</li>
                                                <li>ألوان الأيقونات</li>
                                                <li>تأثيرات hover</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-dark">
                                        <div class="card-header bg-dark text-white">
                                            <h6 class="mb-0">🌙 اختبار الوضع الداكن</h6>
                                        </div>
                                        <div class="card-body">
                                            <p>تأكد من:</p>
                                            <ul>
                                                <li>عدم اختفاء العناصر</li>
                                                <li>تباين الألوان</li>
                                                <li>وضوح النصوص</li>
                                                <li>انتقالات سلسة</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <h6>روابط الاختبار:</h6>
                                <div class="btn-group-vertical w-100">
                                    <a href="admin_dashboard.php" class="btn btn-outline-primary mb-2">
                                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                    </a>
                                    <a href="admin_error_logs.php" class="btn btn-outline-warning mb-2">
                                        <i class="fas fa-exclamation-triangle me-2"></i>سجل الأخطاء
                                    </a>
                                    <a href="admin_users.php" class="btn btn-outline-info mb-2">
                                        <i class="fas fa-users me-2"></i>إدارة المستخدمين
                                    </a>
                                    <a href="admin_activity.php" class="btn btn-outline-secondary mb-2">
                                        <i class="fas fa-history me-2"></i>سجل العمليات
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// اختبار تلقائي للتبديل بين الأوضاع
let testMode = false;

function startAutoTest() {
    if (testMode) return;
    
    testMode = true;
    const button = document.querySelector('button[onclick="toggleTheme()"]');
    
    if (button) {
        let count = 0;
        const interval = setInterval(() => {
            toggleTheme();
            count++;
            
            // فحص العناصر بعد كل تبديل
            setTimeout(() => {
                checkSidebarElements();
            }, 500);
            
            if (count >= 6) { // 3 تبديلات كاملة
                clearInterval(interval);
                testMode = false;
                alert('تم الانتهاء من الاختبار التلقائي!\nتحقق من النتائج في وحدة التحكم.');
            }
        }, 2000);
    }
}

function checkSidebarElements() {
    const sidebar = document.querySelector('.sidebar');
    const navLinks = sidebar.querySelectorAll('.nav-link');
    const navSections = sidebar.querySelectorAll('.nav-section-title');
    
    console.log('=== فحص عناصر القائمة الجانبية ===');
    console.log('عدد الروابط:', navLinks.length);
    console.log('عدد الأقسام:', navSections.length);
    
    let hiddenElements = 0;
    
    navLinks.forEach((link, index) => {
        const style = window.getComputedStyle(link);
        const span = link.querySelector('span');
        const icon = link.querySelector('i');
        
        if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
            console.warn(`الرابط ${index + 1} مخفي:`, link.textContent.trim());
            hiddenElements++;
        }
        
        if (span) {
            const spanStyle = window.getComputedStyle(span);
            if (spanStyle.display === 'none' || spanStyle.visibility === 'hidden' || spanStyle.opacity === '0') {
                console.warn(`نص الرابط ${index + 1} مخفي:`, span.textContent.trim());
                hiddenElements++;
            }
        }
        
        if (icon) {
            const iconStyle = window.getComputedStyle(icon);
            if (iconStyle.display === 'none' || iconStyle.visibility === 'hidden' || iconStyle.opacity === '0') {
                console.warn(`أيقونة الرابط ${index + 1} مخفية`);
                hiddenElements++;
            }
        }
    });
    
    if (hiddenElements === 0) {
        console.log('✅ جميع العناصر ظاهرة بشكل صحيح');
    } else {
        console.error(`❌ يوجد ${hiddenElements} عنصر مخفي`);
    }
}

// إضافة زر الاختبار التلقائي
document.addEventListener('DOMContentLoaded', function() {
    const testButton = document.createElement('button');
    testButton.className = 'btn btn-success btn-sm position-fixed';
    testButton.style.top = '100px';
    testButton.style.left = '20px';
    testButton.style.zIndex = '9999';
    testButton.innerHTML = '<i class="fas fa-play me-1"></i>اختبار تلقائي';
    testButton.onclick = startAutoTest;
    
    document.body.appendChild(testButton);
    
    // فحص أولي
    setTimeout(checkSidebarElements, 1000);
});
</script>

<?php require_once __DIR__ . '/includes/admin_footer.php'; ?>
