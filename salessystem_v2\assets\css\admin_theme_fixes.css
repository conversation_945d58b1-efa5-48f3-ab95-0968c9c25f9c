/* إصلاحات شاملة للوضع الداكن والفاتح في قسم المدير */

/* الوضع الافتراضي (فاتح) */
:root {
    --sidebar-bg-light: linear-gradient(180deg, #0f172a 0%, #1e293b 100%);
    --sidebar-text-light: #94a3b8;
    --sidebar-text-hover-light: #e2e8f0;
    --sidebar-text-active-light: #ffffff;
    --sidebar-bg-hover-light: rgba(71, 85, 105, 0.4);
    --sidebar-bg-active-light: linear-gradient(135deg, rgba(96, 165, 250, 0.3) 0%, rgba(59, 130, 246, 0.2) 100%);
    --sidebar-icon-light: #64748b;
    --sidebar-icon-hover-light: #94a3b8;
    --sidebar-icon-active-light: #60a5fa;
}

/* الوضع الداكن */
[data-theme="dark"] {
    --sidebar-bg-dark: linear-gradient(180deg, #020617 0%, #0f172a 100%);
    --sidebar-text-dark: #64748b;
    --sidebar-text-hover-dark: #94a3b8;
    --sidebar-text-active-dark: #ffffff;
    --sidebar-bg-hover-dark: rgba(71, 85, 105, 0.3);
    --sidebar-bg-active-dark: linear-gradient(135deg, rgba(96, 165, 250, 0.4) 0%, rgba(59, 130, 246, 0.3) 100%);
    --sidebar-icon-dark: #475569;
    --sidebar-icon-hover-dark: #64748b;
    --sidebar-icon-active-dark: #60a5fa;
}

/* تطبيق الأنماط للوضع الفاتح */
.sidebar {
    background: var(--sidebar-bg-light) !important;
    transition: all 0.3s ease;
}

.sidebar .nav-link {
    color: var(--sidebar-text-light) !important;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: var(--sidebar-text-hover-light) !important;
    background: var(--sidebar-bg-hover-light) !important;
}

.sidebar .nav-link.active {
    color: var(--sidebar-text-active-light) !important;
    background: var(--sidebar-bg-active-light) !important;
}

.sidebar .nav-link i {
    color: var(--sidebar-icon-light) !important;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover i {
    color: var(--sidebar-icon-hover-light) !important;
}

.sidebar .nav-link.active i {
    color: var(--sidebar-icon-active-light) !important;
}

/* تطبيق الأنماط للوضع الداكن */
[data-theme="dark"] .sidebar {
    background: var(--sidebar-bg-dark) !important;
}

[data-theme="dark"] .sidebar .nav-link {
    color: var(--sidebar-text-dark) !important;
}

[data-theme="dark"] .sidebar .nav-link:hover {
    color: var(--sidebar-text-hover-dark) !important;
    background: var(--sidebar-bg-hover-dark) !important;
}

[data-theme="dark"] .sidebar .nav-link.active {
    color: var(--sidebar-text-active-dark) !important;
    background: var(--sidebar-bg-active-dark) !important;
}

[data-theme="dark"] .sidebar .nav-link i {
    color: var(--sidebar-icon-dark) !important;
}

[data-theme="dark"] .sidebar .nav-link:hover i {
    color: var(--sidebar-icon-hover-dark) !important;
}

[data-theme="dark"] .sidebar .nav-link.active i {
    color: var(--sidebar-icon-active-dark) !important;
}

/* إصلاح عنوان الشريط الجانبي */
.sidebar .sidebar-brand h5 {
    color: #ffffff !important;
    transition: all 0.3s ease;
}

[data-theme="dark"] .sidebar .sidebar-brand h5 {
    color: #ffffff !important;
}

/* إصلاح النصوص والعناصر الأخرى */
.sidebar .nav-section-title {
    color: #64748b !important;
    transition: all 0.3s ease;
}

[data-theme="dark"] .sidebar .nav-section-title {
    color: #475569 !important;
}

/* إصلاح الحدود */
.sidebar {
    border-right: 1px solid rgba(71, 85, 105, 0.3) !important;
}

[data-theme="dark"] .sidebar {
    border-right: 1px solid rgba(71, 85, 105, 0.4) !important;
}

.sidebar .sidebar-brand {
    border-bottom: 1px solid rgba(71, 85, 105, 0.4) !important;
}

[data-theme="dark"] .sidebar .sidebar-brand {
    border-bottom: 1px solid rgba(71, 85, 105, 0.4) !important;
}

/* إصلاح شريط التمرير */
.sidebar::-webkit-scrollbar-thumb {
    background: rgba(96, 165, 250, 0.3) !important;
    transition: all 0.3s ease;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(96, 165, 250, 0.5) !important;
}

[data-theme="dark"] .sidebar::-webkit-scrollbar-thumb {
    background: rgba(71, 85, 105, 0.5) !important;
}

[data-theme="dark"] .sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(71, 85, 105, 0.7) !important;
}

/* إصلاح التأثيرات الخاصة */
.sidebar .nav-link::before {
    background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.1), transparent) !important;
}

[data-theme="dark"] .sidebar .nav-link::before {
    background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.15), transparent) !important;
}

.sidebar .nav-link.active::after {
    background: linear-gradient(180deg, #60a5fa 0%, #3b82f6 100%) !important;
}

[data-theme="dark"] .sidebar .nav-link.active::after {
    background: linear-gradient(180deg, #60a5fa 0%, #3b82f6 100%) !important;
}

/* إصلاح التحولات السلسة */
.sidebar,
.sidebar .nav-link,
.sidebar .nav-link i,
.sidebar .sidebar-brand,
.sidebar .nav-section-title {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* إصلاح مشاكل الرؤية */
.sidebar .nav-link span {
    opacity: 1 !important;
    visibility: visible !important;
}

[data-theme="dark"] .sidebar .nav-link span {
    opacity: 1 !important;
    visibility: visible !important;
}

/* إصلاح مشاكل الخطوط */
.sidebar {
    font-family: 'Inter', 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* إصلاح مشاكل الحجم والموضع */
.sidebar {
    width: 260px !important;
    min-height: calc(100vh - 76px) !important;
    position: sticky !important;
    top: 76px !important;
}

/* إصلاح مشاكل الاستجابة */
@media (max-width: 768px) {
    .sidebar {
        position: fixed !important;
        top: 76px !important;
        left: -100% !important;
        width: 280px !important;
        height: calc(100vh - 76px) !important;
        z-index: 1000 !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    .sidebar.show {
        left: 0 !important;
    }
}

/* إصلاح مشاكل التداخل */
.sidebar {
    z-index: 100 !important;
}

[data-theme="dark"] .sidebar {
    z-index: 100 !important;
}
