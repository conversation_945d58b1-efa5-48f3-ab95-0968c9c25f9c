/* إصلاحات شاملة للوضع الداكن والفاتح في قسم المدير */

/* الوضع الافتراضي (فاتح) */
:root {
    --sidebar-bg-light: linear-gradient(180deg, #0f172a 0%, #1e293b 100%);
    --sidebar-text-light: #94a3b8;
    --sidebar-text-hover-light: #e2e8f0;
    --sidebar-text-active-light: #ffffff;
    --sidebar-bg-hover-light: rgba(71, 85, 105, 0.4);
    --sidebar-bg-active-light: linear-gradient(135deg, rgba(96, 165, 250, 0.3) 0%, rgba(59, 130, 246, 0.2) 100%);
    --sidebar-icon-light: #64748b;
    --sidebar-icon-hover-light: #94a3b8;
    --sidebar-icon-active-light: #60a5fa;
}

/* الوضع الداكن */
[data-theme="dark"] {
    --sidebar-bg-dark: linear-gradient(180deg, #020617 0%, #0f172a 100%);
    --sidebar-text-dark: #64748b;
    --sidebar-text-hover-dark: #94a3b8;
    --sidebar-text-active-dark: #ffffff;
    --sidebar-bg-hover-dark: rgba(71, 85, 105, 0.3);
    --sidebar-bg-active-dark: linear-gradient(135deg, rgba(96, 165, 250, 0.4) 0%, rgba(59, 130, 246, 0.3) 100%);
    --sidebar-icon-dark: #475569;
    --sidebar-icon-hover-dark: #64748b;
    --sidebar-icon-active-dark: #60a5fa;
}

/* تطبيق الأنماط للوضع الفاتح */
.sidebar {
    background: var(--sidebar-bg-light) !important;
    transition: all 0.3s ease;
}

.sidebar .nav-link {
    color: var(--sidebar-text-light) !important;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: var(--sidebar-text-hover-light) !important;
    background: var(--sidebar-bg-hover-light) !important;
}

.sidebar .nav-link.active {
    color: var(--sidebar-text-active-light) !important;
    background: var(--sidebar-bg-active-light) !important;
}

.sidebar .nav-link i {
    color: var(--sidebar-icon-light) !important;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover i {
    color: var(--sidebar-icon-hover-light) !important;
}

.sidebar .nav-link.active i {
    color: var(--sidebar-icon-active-light) !important;
}

/* تطبيق الأنماط للوضع الداكن */
[data-theme="dark"] .sidebar {
    background: var(--sidebar-bg-dark) !important;
}

[data-theme="dark"] .sidebar .nav-link {
    color: var(--sidebar-text-dark) !important;
}

[data-theme="dark"] .sidebar .nav-link:hover {
    color: var(--sidebar-text-hover-dark) !important;
    background: var(--sidebar-bg-hover-dark) !important;
}

[data-theme="dark"] .sidebar .nav-link.active {
    color: var(--sidebar-text-active-dark) !important;
    background: var(--sidebar-bg-active-dark) !important;
}

[data-theme="dark"] .sidebar .nav-link i {
    color: var(--sidebar-icon-dark) !important;
}

[data-theme="dark"] .sidebar .nav-link:hover i {
    color: var(--sidebar-icon-hover-dark) !important;
}

[data-theme="dark"] .sidebar .nav-link.active i {
    color: var(--sidebar-icon-active-dark) !important;
}

/* إصلاح عنوان الشريط الجانبي */
.sidebar .sidebar-brand h5 {
    color: #ffffff !important;
    transition: all 0.3s ease;
}

[data-theme="dark"] .sidebar .sidebar-brand h5 {
    color: #ffffff !important;
}

/* إصلاح النصوص والعناصر الأخرى */
.sidebar .nav-section-title {
    color: #64748b !important;
    transition: all 0.3s ease;
}

[data-theme="dark"] .sidebar .nav-section-title {
    color: #475569 !important;
}

/* إصلاح الحدود */
.sidebar {
    border-right: 1px solid rgba(71, 85, 105, 0.3) !important;
}

[data-theme="dark"] .sidebar {
    border-right: 1px solid rgba(71, 85, 105, 0.4) !important;
}

.sidebar .sidebar-brand {
    border-bottom: 1px solid rgba(71, 85, 105, 0.4) !important;
}

[data-theme="dark"] .sidebar .sidebar-brand {
    border-bottom: 1px solid rgba(71, 85, 105, 0.4) !important;
}

/* إصلاح شريط التمرير */
.sidebar::-webkit-scrollbar-thumb {
    background: rgba(96, 165, 250, 0.3) !important;
    transition: all 0.3s ease;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(96, 165, 250, 0.5) !important;
}

[data-theme="dark"] .sidebar::-webkit-scrollbar-thumb {
    background: rgba(71, 85, 105, 0.5) !important;
}

[data-theme="dark"] .sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(71, 85, 105, 0.7) !important;
}

/* إصلاح التأثيرات الخاصة */
.sidebar .nav-link::before {
    background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.1), transparent) !important;
}

[data-theme="dark"] .sidebar .nav-link::before {
    background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.15), transparent) !important;
}

.sidebar .nav-link.active::after {
    background: linear-gradient(180deg, #60a5fa 0%, #3b82f6 100%) !important;
}

[data-theme="dark"] .sidebar .nav-link.active::after {
    background: linear-gradient(180deg, #60a5fa 0%, #3b82f6 100%) !important;
}

/* إصلاح التحولات السلسة */
.sidebar,
.sidebar .nav-link,
.sidebar .nav-link i,
.sidebar .sidebar-brand,
.sidebar .nav-section-title {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* إصلاح مشاكل الرؤية - ضمان عدم اختفاء العناصر */
.sidebar .nav-link,
.sidebar .nav-link span,
.sidebar .nav-link i,
.sidebar .nav-section-title,
.sidebar .sidebar-brand,
.sidebar .sidebar-brand h5 {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

[data-theme="dark"] .sidebar .nav-link,
[data-theme="dark"] .sidebar .nav-link span,
[data-theme="dark"] .sidebar .nav-link i,
[data-theme="dark"] .sidebar .nav-section-title,
[data-theme="dark"] .sidebar .sidebar-brand,
[data-theme="dark"] .sidebar .sidebar-brand h5 {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* إصلاح مشاكل العرض */
.sidebar .nav-item {
    display: block !important;
}

.sidebar .nav-section {
    display: block !important;
}

[data-theme="dark"] .sidebar .nav-item {
    display: block !important;
}

[data-theme="dark"] .sidebar .nav-section {
    display: block !important;
}

/* إصلاح مشاكل الخطوط */
.sidebar {
    font-family: 'Inter', 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* إصلاح مشاكل الحجم والموضع */
.sidebar {
    width: 260px !important;
    min-height: calc(100vh - 76px) !important;
    position: sticky !important;
    top: 76px !important;
}

/* إصلاح مشاكل الاستجابة */
@media (max-width: 768px) {
    .sidebar {
        position: fixed !important;
        top: 76px !important;
        left: -100% !important;
        width: 280px !important;
        height: calc(100vh - 76px) !important;
        z-index: 1000 !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    .sidebar.show {
        left: 0 !important;
    }
}

/* إصلاح مشاكل التداخل */
.sidebar {
    z-index: 100 !important;
}

[data-theme="dark"] .sidebar {
    z-index: 100 !important;
}

/* إصلاحات إضافية لضمان الثبات الكامل */
.sidebar * {
    box-sizing: border-box !important;
}

.sidebar .nav-link {
    position: relative !important;
    overflow: visible !important;
}

.sidebar .nav-link span {
    position: relative !important;
    z-index: 2 !important;
}

.sidebar .nav-link i {
    position: relative !important;
    z-index: 2 !important;
}

/* منع أي تأثيرات قد تخفي العناصر */
.sidebar .nav-link:not(.active) {
    transform: none !important;
}

.sidebar .nav-link:not(.active) span,
.sidebar .nav-link:not(.active) i {
    transform: none !important;
}

/* ضمان الألوان الثابتة */
.sidebar .nav-section-title {
    color: #64748b !important;
    font-weight: 600 !important;
    font-size: 11px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    margin: 16px 0 8px 0 !important;
    padding: 0 16px !important;
}

[data-theme="dark"] .sidebar .nav-section-title {
    color: #475569 !important;
}

/* إصلاح مشاكل التحولات */
.sidebar,
.sidebar * {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* منع أي overflow قد يخفي العناصر */
.sidebar {
    overflow-x: visible !important;
    overflow-y: auto !important;
}

.sidebar .position-sticky {
    overflow: visible !important;
}

/* ضمان عرض جميع العناصر */
.sidebar ul,
.sidebar li,
.sidebar a,
.sidebar div {
    display: block !important;
}

.sidebar .nav {
    display: flex !important;
    flex-direction: column !important;
}

.sidebar .nav-item {
    display: block !important;
    width: 100% !important;
}

.sidebar .nav-link {
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
    padding: 12px 16px !important;
    text-decoration: none !important;
}

.sidebar .nav-link i {
    display: inline-block !important;
    width: 20px !important;
    margin-left: 12px !important;
    text-align: center !important;
}

.sidebar .nav-link span {
    display: inline-block !important;
    flex: 1 !important;
}
