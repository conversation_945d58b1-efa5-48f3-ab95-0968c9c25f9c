<?php /** * لوحة تحكم المدير الرئيسية المحدثة */ require_once __DIR__ . '/config/init.php'; // التحقق من تسجيل دخول المدير if (!isAdminLoggedIn()) { header("Location: admin_login.php"); exit(); } try { // جلب إحصائيات النظام $db = getUnifiedDB(); if (!$db) { throw new Exception("فشل الاتصال بقاعدة البيانات"); } // إحصائيات المستخدمين (تصحيح أسماء الأعمدة) $users_stats_query = "SELECT COUNT(*) as total_users, COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users, COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent_users, COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as new_users_week FROM users"; $users_stats_result = $db->query($users_stats_query); if (!$users_stats_result) { ErrorHandler::logDatabaseError($users_stats_query, $db->error); throw new Exception("خطأ في جلب إحصائيات المستخدمين"); } $users_stats = $users_stats_result->fetch_assoc(); // إحصائيات العمليات اليومية $today_activity_query = "SELECT COUNT(*) as total_activities, COUNT(CASE WHEN user_type = 'user' THEN 1 END) as user_activities, COUNT(CASE WHEN user_type = 'admin' THEN 1 END) as admin_activities, COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as recent_activities FROM activity_log WHERE DATE(created_at) = CURDATE()"; $today_activity_result = $db->query($today_activity_query); if (!$today_activity_result) { ErrorHandler::logDatabaseError($today_activity_query, $db->error); throw new Exception("خطأ في جلب إحصائيات العمليات"); } $today_activity = $today_activity_result->fetch_assoc(); // إحصائيات المبيعات والمشتريات اليومية $financial_stats_query = "SELECT COALESCE(SUM(CASE WHEN DATE(created_at) = CURDATE() THEN total_amount END), 0) as today_sales, COALESCE(COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END), 0) as today_invoices, COALESCE(SUM(CASE WHEN DATE(created_at) >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN total_amount END), 0) as week_sales, COALESCE(COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END), 0) as week_invoices FROM sales"; $financial_stats_result = $db->query($financial_stats_query); if (!$financial_stats_result) { ErrorHandler::logDatabaseError($financial_stats_query, $db->error); $financial_stats = ['today_sales' => 0, 'today_invoices' => 0, 'week_sales' => 0, 'week_invoices' => 0]; } else { $financial_stats = $financial_stats_result->fetch_assoc(); } // إحصائيات الأخطاء اليومية $error_logs = ErrorHandler::getLogs(date('Y-m-d'), null, 1000); $error_stats = [ 'total_errors' => count($error_logs), 'critical_errors' => count(array_filter($error_logs, function($log) { return in_array($log['level'], ['CRITICAL', 'ERROR', 'FATAL']); })), 'warnings' => count(array_filter($error_logs, function($log) { return in_array($log['level'], ['WARNING', 'NOTICE']); })), 'database_errors' => count(array_filter($error_logs, function($log) { return $log['level'] === 'DATABASE'; })) ]; // أحدث العمليات (تصحيح JOIN مع الجداول الصحيحة) $recent_activities_query = "SELECT al.*, CASE WHEN al.user_type = 'admin' THEN COALESCE(a.full_name, a.username, 'مدير غير معروف') ELSE COALESCE(u.full_name, u.username, 'مستخدم غير معروف') END as user_name FROM activity_log al LEFT JOIN admins a ON al.user_id = a.id AND al.user_type = 'admin' LEFT JOIN users u ON al.user_id = u.id AND al.user_type = 'user' ORDER BY al.created_at DESC LIMIT 15"; $recent_activities_result = $db->query($recent_activities_query); if (!$recent_activities_result) { ErrorHandler::logDatabaseError($recent_activities_query, $db->error); $recent_activities = []; } else { $recent_activities = []; while ($row = $recent_activities_result->fetch_assoc()) { $recent_activities[] = $row; } } } catch (Exception $e) { ErrorHandler::logError('ERROR', 'Admin dashboard error: ' . $e->getMessage(), __FILE__, __LINE__); $_SESSION['error'] = 'حدث خطأ في تحميل لوحة التحكم: ' . $e->getMessage(); // قيم افتراضية في حالة الخطأ $users_stats = ['total_users' => 0, 'active_users' => 0, 'recent_users' => 0, 'new_users_week' => 0]; $today_activity = ['total_activities' => 0, 'user_activities' => 0, 'admin_activities' => 0, 'recent_activities' => 0]; $financial_stats = ['today_sales' => 0, 'today_invoices' => 0, 'week_sales' => 0, 'week_invoices' => 0]; $error_stats = ['total_errors' => 0, 'critical_errors' => 0, 'warnings' => 0, 'database_errors' => 0]; $recent_activities = []; } require_once __DIR__ . '/includes/admin_header_new.php'; ?> <div class="container-fluid"> <div class="row"> <!-- الشريط الجانبي المتطور --> <nav class="modern-sidebar d-none d-lg-block"> <div class="sidebar-section"> <div class="sidebar-section-title">الإدارة الرئيسية</div> <a class="sidebar-nav-link active" href="admin_dashboard.php"> <i class="fas fa-tachometer-alt"></i> <span>لوحة التحكم</span> </a> <a class="sidebar-nav-link" href="admin_users.php"> <i class="fas fa-users"></i> <span>إدارة المستخدمين</span> </a> <a class="sidebar-nav-link" href="admin_activity.php"> <i class="fas fa-history"></i> <span>سجل العمليات</span> </a> </div> <div class="sidebar-section"> <div class="sidebar-section-title">التقارير والإحصائيات</div> <a class="sidebar-nav-link" href="admin_reports.php"> <i class="fas fa-chart-bar"></i> <span>التقارير الشاملة</span> </a> <a class="sidebar-nav-link" href="admin_financial.php"> <i class="fas fa-file-invoice-dollar"></i> <span>التقارير المالية</span> </a> </div> <div class="sidebar-section"> <div class="sidebar-section-title">إدارة النظام</div> <a class="sidebar-nav-link" href="admin_error_logs.php"> <i class="fas fa-exclamation-triangle"></i> <span>سجل الأخطاء</span> </a> <a class="sidebar-nav-link" href="admin_system.php"> <i class="fas fa-cogs"></i> <span>إعدادات النظام</span> </a> </div> <div class="sidebar-section"> <div class="sidebar-section-title">إدارة المديرين</div> <a class="sidebar-nav-link" href="admin_manage_admins.php"> <i class="fas fa-user-shield"></i> <span>إدارة المديرين</span> </a> </div> </nav> <!-- المحتوى الرئيسي المتطور --> <main class="admin-content" id="main-content"> <!-- رأس الصفحة --> <div class="d-flex justify-content-between align-items-center mb-4 "> <div> <h1 class="h2 mb-2 fw-bold gradient-text"> <i class="fas fa-crown me-3"></i> لوحة تحكم المدير </h1> <p class="text-muted mb-0">مرحباً بك في نظام إدارة المبيعات المتطور</p> </div> <div class="d-flex gap-2"> <button type="button" class="modern-btn modern-btn-primary"> <i class="fas fa-plus"></i> <span>إضافة جديد</span> </button> <button type="button" class="modern-btn modern-btn-outline"> <i class="fas fa-download"></i> <span>تصدير</span> </button> <button type="button" class="modern-btn modern-btn-secondary" onclick="location.reload()"> <i class="fas fa-sync-alt"></i> <span>تحديث</span> </button> </div> </div> <!-- بطاقات الإحصائيات المتطورة --> <div class="row g-4 mb-5"> <!-- إجمالي المستخدمين --> <div class="col-xl-3 col-md-6"> <div class="stats-card "> <div class="d-flex justify-content-between align-items-start"> <div class="flex-1"> <div class="stats-label">إجمالي المستخدمين</div> <div class="stats-value"> <?php echo number_format($users_stats['total_users']); ?> </div> <div class="stats-change text-success"> <i class="fas fa-arrow-up me-1"></i> +<?php echo number_format($users_stats['new_users_week']); ?> هذا الأسبوع </div> </div> <div class="stats-icon"> <i class="fas fa-users"></i> </div> </div> </div> </div> <!-- المستخدمين النشطين --> <div class="col-xl-3 col-md-6"> <div class="stats-card " > <div class="d-flex justify-content-between align-items-start"> <div class="flex-1"> <div class="stats-label">المستخدمين النشطين</div> <div class="stats-value"> <?php echo number_format($users_stats['active_users']); ?> </div> <div class="stats-change text-muted"> <i class="fas fa-clock me-1"></i> آخر 30 يوم: <?php echo number_format($users_stats['recent_users']); ?> </div> </div> <div class="stats-icon" style="background: var(--success-gradient);"> <i class="fas fa-user-check"></i> </div> </div> </div> </div> <!-- العمليات اليومية --> <div class="col-xl-3 col-md-6"> <div class="stats-card " > <div class="d-flex justify-content-between align-items-start"> <div class="flex-1"> <div class="stats-label">عمليات اليوم</div> <div class="stats-value"> <?php echo number_format($today_activity['total_activities']); ?> </div> <div class="stats-change text-info"> <i class="fas fa-clock me-1"></i> آخر ساعة: <?php echo number_format($today_activity['recent_activities']); ?> </div> </div> <div class="stats-icon" style="background: var(--info-gradient);"> <i class="fas fa-chart-line"></i> </div> </div> </div> </div> <!-- حالة النظام --> <div class="col-xl-3 col-md-6"> <div class="stats-card " > <div class="d-flex justify-content-between align-items-start"> <div class="flex-1"> <div class="stats-label">حالة النظام</div> <div class="stats-value"> <?php echo $error_stats['critical_errors'] > 0 ? 'تحتاج انتباه' : 'مستقر'; ?> </div> <div class="stats-change <?php echo $error_stats['total_errors'] > 0 ? 'text-warning' : 'text-success'; ?>"> <i class="fas fa-exclamation-triangle me-1"></i> <?php echo number_format($error_stats['total_errors']); ?> خطأ اليوم </div> </div> <div class="stats-icon" style="background: <?php echo $error_stats['critical_errors'] > 0 ? 'var(--danger-gradient)' : 'var(--success-gradient)'; ?>;"> <i class="fas fa-shield-alt"></i> </div> </div> </div> </div> </div> <!-- الإحصائيات المالية المتطورة --> <div class="row g-4 mb-5"> <!-- مبيعات اليوم --> <div class="col-xl-3 col-md-6"> <div class="stats-card " > <div class="d-flex justify-content-between align-items-start"> <div class="flex-1"> <div class="stats-label">مبيعات اليوم</div> <div class="stats-value"> <?php echo number_format($financial_stats['today_sales'], 2); ?> ر.س </div> <div class="stats-change text-muted"> <i class="fas fa-file-invoice me-1"></i> <?php echo number_format($financial_stats['today_invoices']); ?> فاتورة </div> </div> <div class="stats-icon" style="background: var(--success-gradient);"> <i class="fas fa-dollar-sign"></i> </div> </div> </div> </div> <!-- مبيعات الأسبوع --> <div class="col-xl-3 col-md-6"> <div class="stats-card " > <div class="d-flex justify-content-between align-items-start"> <div class="flex-1"> <div class="stats-label">مبيعات الأسبوع</div> <div class="stats-value"> <?php echo number_format($financial_stats['week_sales'], 2); ?> ر.س </div> <div class="stats-change text-muted"> <i class="fas fa-file-invoice me-1"></i> <?php echo number_format($financial_stats['week_invoices']); ?> فاتورة </div> </div> <div class="stats-icon"> <i class="fas fa-chart-bar"></i> </div> </div> </div> </div> <!-- أخطاء حرجة --> <div class="col-xl-3 col-md-6"> <div class="stats-card " > <div class="d-flex justify-content-between align-items-start"> <div class="flex-1"> <div class="stats-label">أخطاء حرجة</div> <div class="stats-value"> <?php echo number_format($error_stats['critical_errors']); ?> </div> <div class="stats-change text-warning"> <i class="fas fa-database me-1"></i> قاعدة البيانات: <?php echo number_format($error_stats['database_errors']); ?> </div> </div> <div class="stats-icon" style="background: var(--danger-gradient);"> <i class="fas fa-exclamation-circle"></i> </div> </div> </div> </div> <!-- تحذيرات --> <div class="col-xl-3 col-md-6"> <div class="stats-card " > <div class="d-flex justify-content-between align-items-start"> <div class="flex-1"> <div class="stats-label">تحذيرات</div> <div class="stats-value"> <?php echo number_format($error_stats['warnings']); ?> </div> <div class="stats-change text-muted"> <i class="fas fa-eye me-1"></i> <a href="admin_error_logs.php" class="text-decoration-none">عرض السجل</a> </div> </div> <div class="stats-icon" style="background: var(--warning-gradient);"> <i class="fas fa-exclamation-triangle"></i> </div> </div> </div> </div> </div> <!-- أحدث العمليات المتطورة --> <div class="modern-card " > <div class="modern-card-header"> <h5 class="mb-0 fw-bold gradient-text"> <i class="fas fa-history me-2"></i>أحدث العمليات </h5> <a href="admin_activity.php" class="modern-btn modern-btn-outline"> <i class="fas fa-external-link-alt"></i> <span>عرض الكل</span> </a> </div> <div class="modern-card-body p-0"> <div class="table-responsive"> <table class="modern-table table mb-0"> <thead> <tr> <th>الوقت</th> <th>المستخدم</th> <th>النوع</th> <th>العملية</th> <th>الوصف</th> <th>IP</th> </tr> </thead> <tbody> <?php if (!empty($recent_activities)): ?> <?php foreach ($recent_activities as $activity): ?> <tr> <td> <span class="modern-badge modern-badge-primary"> <?php echo date('H:i:s', strtotime($activity['created_at'])); ?> </span> </td> <td><?php echo htmlspecialchars($activity['user_name'] ?? 'غير محدد'); ?></td> <td> <span class="modern-badge <?php echo $activity['user_type'] === 'admin' ? 'modern-badge-danger' : 'modern-badge-primary'; ?>"> <?php echo $activity['user_type'] === 'admin' ? 'مدير' : 'مستخدم'; ?> </span> </td> <td> <?php $action_icons = [ 'user_login' => 'fas fa-sign-in-alt text-success', 'user_logout' => 'fas fa-sign-out-alt text-warning', 'create' => 'fas fa-plus text-success', 'update' => 'fas fa-edit text-primary', 'delete' => 'fas fa-trash text-danger', 'view' => 'fas fa-eye text-info' ]; $icon = $action_icons[$activity['action']] ?? 'fas fa-circle text-secondary'; ?> <i class="<?php echo $icon; ?> me-2"></i> <span class="modern-badge modern-badge-success"><?php echo htmlspecialchars($activity['action']); ?></span> </td> <td><?php echo htmlspecialchars($activity['description'] ?? ''); ?></td> <td class="text-muted"><?php echo htmlspecialchars($activity['ip_address'] ?? ''); ?></td> </tr> <?php endforeach; ?> <?php else: ?> <tr> <td colspan="6" class="text-center text-muted py-3"> <i class="fas fa-info-circle me-2"></i> لا توجد عمليات حديثة </td> </tr> <?php endif; ?> </tbody> </table> </div> </div> </div> </main> </div> </div> <!-- زر الإجراءات السريعة العائم --> <button class="floating-action" onclick="showQuickActions()" title="الإجراءات السريعة"> <i class="fas fa-plus"></i> </button> <!-- قائمة الإجراءات السريعة --> <div id="quickActionsMenu" class="position-fixed" style="bottom: 5rem; right: 2rem; z-index: 999; display: none;"> <div class="d-flex flex-column gap-2"> <a href="admin_users.php" class="modern-btn modern-btn-primary"> <i class="fas fa-user-plus"></i> <span>إضافة مستخدم</span> </a> <a href="admin_reports.php" class="modern-btn modern-btn-secondary"> <i class="fas fa-chart-bar"></i> <span>تقرير سريع</span> </a> <a href="admin_system.php" class="modern-btn modern-btn-success"> <i class="fas fa-cogs"></i> <span>الإعدادات</span> </a> </div> </div> <script> // إظهار/إخفاء قائمة الإجراءات السريعة function showQuickActions() { const menu = document.getElementById('quickActionsMenu'); const isVisible = menu.style.display !== 'none'; menu.style.display = isVisible ? 'none' : 'block'; if (!isVisible) { menu.style.animation = 'fadeIn 0.3s ease-out'; } } // إخفاء القائمة عند النقر خارجها document.addEventListener('click', function(e) { const menu = document.getElementById('quickActionsMenu'); const button = document.querySelector('.floating-action'); if (!menu.contains(e.target) && !button.contains(e.target)) { menu.style.display = 'none'; } }); </script> <?php require_once __DIR__ . '/includes/admin_footer_new.php'; ?> 