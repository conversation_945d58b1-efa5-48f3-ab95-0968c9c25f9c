<?php
/**
 * لوحة تحكم المدير الرئيسية المحدثة
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

try {
    // جلب إحصائيات النظام
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    // إحصائيات المستخدمين (تصحيح أسماء الأعمدة)
    $users_stats_query = "SELECT
        COUNT(*) as total_users,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
        COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent_users,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as new_users_week
        FROM users";

    $users_stats_result = $db->query($users_stats_query);
    if (!$users_stats_result) {
        ErrorHandler::logDatabaseError($users_stats_query, $db->error);
        throw new Exception("خطأ في جلب إحصائيات المستخدمين");
    }
    $users_stats = $users_stats_result->fetch_assoc();

    // إحصائيات العمليات اليومية
    $today_activity_query = "SELECT
        COUNT(*) as total_activities,
        COUNT(CASE WHEN user_type = 'user' THEN 1 END) as user_activities,
        COUNT(CASE WHEN user_type = 'admin' THEN 1 END) as admin_activities,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as recent_activities
        FROM activity_log
        WHERE DATE(created_at) = CURDATE()";

    $today_activity_result = $db->query($today_activity_query);
    if (!$today_activity_result) {
        ErrorHandler::logDatabaseError($today_activity_query, $db->error);
        throw new Exception("خطأ في جلب إحصائيات العمليات");
    }
    $today_activity = $today_activity_result->fetch_assoc();

    // إحصائيات المبيعات والمشتريات اليومية
    $financial_stats_query = "SELECT
        COALESCE(SUM(CASE WHEN DATE(created_at) = CURDATE() THEN total_amount END), 0) as today_sales,
        COALESCE(COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END), 0) as today_invoices,
        COALESCE(SUM(CASE WHEN DATE(created_at) >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN total_amount END), 0) as week_sales,
        COALESCE(COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END), 0) as week_invoices
        FROM sales";

    $financial_stats_result = $db->query($financial_stats_query);
    if (!$financial_stats_result) {
        ErrorHandler::logDatabaseError($financial_stats_query, $db->error);
        $financial_stats = ['today_sales' => 0, 'today_invoices' => 0, 'week_sales' => 0, 'week_invoices' => 0];
    } else {
        $financial_stats = $financial_stats_result->fetch_assoc();
    }

    // إحصائيات الأخطاء اليومية
    $error_logs = ErrorHandler::getLogs(date('Y-m-d'), null, 1000);
    $error_stats = [
        'total_errors' => count($error_logs),
        'critical_errors' => count(array_filter($error_logs, function($log) {
            return in_array($log['level'], ['CRITICAL', 'ERROR', 'FATAL']);
        })),
        'warnings' => count(array_filter($error_logs, function($log) {
            return in_array($log['level'], ['WARNING', 'NOTICE']);
        })),
        'database_errors' => count(array_filter($error_logs, function($log) {
            return $log['level'] === 'DATABASE';
        }))
    ];

    // أحدث العمليات (تصحيح JOIN مع الجداول الصحيحة)
    $recent_activities_query = "SELECT
        al.*,
        CASE
            WHEN al.user_type = 'admin' THEN COALESCE(a.full_name, a.username, 'مدير غير معروف')
            ELSE COALESCE(u.full_name, u.username, 'مستخدم غير معروف')
        END as user_name
        FROM activity_log al
        LEFT JOIN admins a ON al.user_id = a.id AND al.user_type = 'admin'
        LEFT JOIN users u ON al.user_id = u.id AND al.user_type = 'user'
        ORDER BY al.created_at DESC
        LIMIT 15";

    $recent_activities_result = $db->query($recent_activities_query);
    if (!$recent_activities_result) {
        ErrorHandler::logDatabaseError($recent_activities_query, $db->error);
        $recent_activities = [];
    } else {
        $recent_activities = [];
        while ($row = $recent_activities_result->fetch_assoc()) {
            $recent_activities[] = $row;
        }
    }

} catch (Exception $e) {
    ErrorHandler::logError('ERROR', 'Admin dashboard error: ' . $e->getMessage(), __FILE__, __LINE__);
    $_SESSION['error'] = 'حدث خطأ في تحميل لوحة التحكم: ' . $e->getMessage();

    // قيم افتراضية في حالة الخطأ
    $users_stats = ['total_users' => 0, 'active_users' => 0, 'recent_users' => 0, 'new_users_week' => 0];
    $today_activity = ['total_activities' => 0, 'user_activities' => 0, 'admin_activities' => 0, 'recent_activities' => 0];
    $financial_stats = ['today_sales' => 0, 'today_invoices' => 0, 'week_sales' => 0, 'week_invoices' => 0];
    $error_stats = ['total_errors' => 0, 'critical_errors' => 0, 'warnings' => 0, 'database_errors' => 0];
    $recent_activities = [];
}

require_once __DIR__ . '/includes/admin_header.php';
?>

<?php include __DIR__ . '/includes/admin_navbar.php'; ?>

<div class="container-fluid">
    <div class="row">
        <?php include __DIR__ . '/includes/admin_sidebar.php'; ?>

        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                <div>
                    <h1 class="h3 mb-1 fw-bold text-primary">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة تحكم المدير
                    </h1>
                    <p class="text-muted mb-0 small">مرحباً بك في نظام إدارة المبيعات المتقدم</p>
                </div>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>إضافة جديد
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-sync-alt me-1"></i>تحديث
                        </button>
                    </div>
                </div>
            </div>

            <!-- البطاقات الإحصائية المحدثة والمحسنة -->
            <div class="row mb-4">
                <!-- إحصائيات المستخدمين -->
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card stats-card border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-label">إجمالي المستخدمين</div>
                                    <div class="stats-value text-primary">
                                        <?php echo number_format($users_stats['total_users']); ?>
                                    </div>
                                    <div class="stats-change text-success">
                                        <i class="fas fa-arrow-up"></i>
                                        +<?php echo number_format($users_stats['new_users_week']); ?> هذا الأسبوع
                                    </div>
                                </div>
                                <div class="stats-icon bg-primary">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المستخدمين النشطين -->
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card stats-card border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-label">المستخدمين النشطين</div>
                                    <div class="stats-value text-success">
                                        <?php echo number_format($users_stats['active_users']); ?>
                                    </div>
                                    <div class="stats-change text-muted">
                                        <i class="fas fa-clock"></i>
                                        آخر 30 يوم: <?php echo number_format($users_stats['recent_users']); ?>
                                    </div>
                                </div>
                                <div class="stats-icon bg-success">
                                    <i class="fas fa-user-check"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- العمليات اليومية -->
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card stats-card border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-label">عمليات اليوم</div>
                                    <div class="stats-value text-info">
                                        <?php echo number_format($today_activity['total_activities']); ?>
                                    </div>
                                    <div class="stats-change text-info">
                                        <i class="fas fa-clock"></i>
                                        آخر ساعة: <?php echo number_format($today_activity['recent_activities']); ?>
                                    </div>
                                </div>
                                <div class="stats-icon bg-info">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- حالة النظام -->
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card stats-card border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-label">حالة النظام</div>
                                    <div class="stats-value <?php echo $error_stats['critical_errors'] > 0 ? 'text-danger' : 'text-success'; ?>">
                                        <?php echo $error_stats['critical_errors'] > 0 ? 'تحتاج انتباه' : 'مستقر'; ?>
                                    </div>
                                    <div class="stats-change <?php echo $error_stats['total_errors'] > 0 ? 'text-warning' : 'text-success'; ?>">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <?php echo number_format($error_stats['total_errors']); ?> خطأ اليوم
                                    </div>
                                </div>
                                <div class="stats-icon <?php echo $error_stats['critical_errors'] > 0 ? 'bg-danger' : 'bg-success'; ?>">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- صف إضافي للإحصائيات المالية -->
            <div class="row mb-4">
                <!-- مبيعات اليوم -->
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card stats-card border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-label">مبيعات اليوم</div>
                                    <div class="stats-value text-success">
                                        <?php echo number_format($financial_stats['today_sales'], 2); ?> ر.س
                                    </div>
                                    <div class="stats-change text-muted">
                                        <i class="fas fa-file-invoice"></i>
                                        <?php echo number_format($financial_stats['today_invoices']); ?> فاتورة
                                    </div>
                                </div>
                                <div class="stats-icon bg-success">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- مبيعات الأسبوع -->
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card stats-card border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-label">مبيعات الأسبوع</div>
                                    <div class="stats-value text-primary">
                                        <?php echo number_format($financial_stats['week_sales'], 2); ?> ر.س
                                    </div>
                                    <div class="stats-change text-muted">
                                        <i class="fas fa-file-invoice"></i>
                                        <?php echo number_format($financial_stats['week_invoices']); ?> فاتورة
                                    </div>
                                </div>
                                <div class="stats-icon bg-primary">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أخطاء حرجة -->
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card stats-card border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-label">أخطاء حرجة</div>
                                    <div class="stats-value text-danger">
                                        <?php echo number_format($error_stats['critical_errors']); ?>
                                    </div>
                                    <div class="stats-change text-warning">
                                        <i class="fas fa-database"></i>
                                        قاعدة البيانات: <?php echo number_format($error_stats['database_errors']); ?>
                                    </div>
                                </div>
                                <div class="stats-icon bg-danger">
                                    <i class="fas fa-exclamation-circle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تحذيرات -->
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card stats-card border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-label">تحذيرات</div>
                                    <div class="stats-value text-warning">
                                        <?php echo number_format($error_stats['warnings']); ?>
                                    </div>
                                    <div class="stats-change text-muted">
                                        <i class="fas fa-eye"></i>
                                        <a href="admin_error_logs.php" class="text-decoration-none">عرض السجل</a>
                                    </div>
                                </div>
                                <div class="stats-icon bg-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أحدث العمليات -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-history me-2"></i>أحدث العمليات
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>المستخدم</th>
                                    <th>النوع</th>
                                    <th>العملية</th>
                                    <th>الوصف</th>
                                    <th>IP</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($recent_activities)): ?>
                                    <?php foreach ($recent_activities as $activity): ?>
                                    <tr>
                                        <td class="small"><?php echo date('H:i:s', strtotime($activity['created_at'])); ?></td>
                                        <td class="small"><?php echo htmlspecialchars($activity['user_name'] ?? 'غير محدد'); ?></td>
                                        <td>
                                            <span class="badge <?php echo $activity['user_type'] === 'admin' ? 'bg-danger' : 'bg-primary'; ?>">
                                                <?php echo $activity['user_type'] === 'admin' ? 'مدير' : 'مستخدم'; ?>
                                            </span>
                                        </td>
                                        <td class="small">
                                            <?php
                                            $action_icons = [
                                                'user_login' => 'fas fa-sign-in-alt text-success',
                                                'user_logout' => 'fas fa-sign-out-alt text-warning',
                                                'create' => 'fas fa-plus text-success',
                                                'update' => 'fas fa-edit text-primary',
                                                'delete' => 'fas fa-trash text-danger',
                                                'view' => 'fas fa-eye text-info'
                                            ];
                                            $icon = $action_icons[$activity['action']] ?? 'fas fa-circle text-secondary';
                                            ?>
                                            <i class="<?php echo $icon; ?> me-1"></i>
                                            <code><?php echo htmlspecialchars($activity['action']); ?></code>
                                        </td>
                                        <td class="small"><?php echo htmlspecialchars($activity['description'] ?? ''); ?></td>
                                        <td class="small text-muted"><?php echo htmlspecialchars($activity['ip_address'] ?? ''); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="text-center text-muted py-3">
                                            <i class="fas fa-info-circle me-2"></i>
                                            لا توجد عمليات حديثة
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<style>
/* البطاقات الإحصائية المحسنة */
.stats-card {
    background: var(--admin-bg-card);
    border-radius: var(--admin-border-radius-lg);
    box-shadow: var(--admin-shadow-sm);
    transition: var(--admin-transition);
    border: 1px solid var(--admin-border-color);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--admin-gradient-primary);
    opacity: 0;
    transition: var(--admin-transition);
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--admin-shadow-lg);
}

.stats-card:hover::before {
    opacity: 1;
}

.stats-label {
    font-size: 12px;
    font-weight: 600;
    color: var(--admin-gray);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.stats-value {
    font-size: 28px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 4px;
}

.stats-change {
    font-size: 11px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.stats-icon.bg-primary {
    background: var(--admin-gradient-primary);
}

.stats-icon.bg-success {
    background: var(--admin-gradient-success);
}

.stats-icon.bg-info {
    background: var(--admin-gradient-primary);
}

.stats-icon.bg-warning {
    background: var(--admin-gradient-warning);
}

.stats-icon.bg-danger {
    background: var(--admin-gradient-danger);
}

/* تحسينات عامة */
.text-xs {
    font-size: 11px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.h5 {
    font-size: 18px;
    font-weight: 700;
}

code {
    background: rgba(37, 99, 235, 0.1);
    color: var(--admin-royal-blue);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

.small {
    font-size: 12px;
}

/* تحسين الجداول */
.table th {
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--admin-slate);
    border-bottom: 2px solid var(--admin-border-color);
    padding: 12px 8px;
}

.table td {
    padding: 10px 8px;
    border-bottom: 1px solid var(--admin-border-light);
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: var(--admin-light);
}

/* تحسين الشارات */
.badge {
    font-size: 10px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 6px;
}

/* تحسين الوضع الداكن */
[data-theme="dark"] .stats-card {
    background: var(--admin-bg-card);
    border-color: var(--admin-border-color);
}

[data-theme="dark"] .stats-label {
    color: var(--admin-silver);
}

[data-theme="dark"] .table th {
    color: var(--admin-slate);
    border-bottom-color: var(--admin-border-color);
}

[data-theme="dark"] .table td {
    border-bottom-color: var(--admin-border-light);
}

[data-theme="dark"] .table tbody tr:hover {
    background-color: var(--admin-border-light);
}

[data-theme="dark"] code {
    background: rgba(37, 99, 235, 0.2);
    color: #60a5fa;
}
</style>

<?php require_once __DIR__ . '/includes/admin_footer.php'; ?>
