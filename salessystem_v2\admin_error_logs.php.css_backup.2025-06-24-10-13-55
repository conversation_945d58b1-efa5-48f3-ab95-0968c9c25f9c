<?php
 /** * صفحة عرض سجل الأخطاء للمدير */ require_once __DIR__ . '/config/init.php';
 // التحقق من تسجيل دخول المدير if (!isAdminLoggedIn()) {
 header("Location: admin_login.php");
 exit();
}
 // التحقق من الصلاحيات if (!hasAdminPermission('view_system_logs')) {
 $_SESSION[
    'error'
] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
 header("Location: admin_dashboard.php");
 exit();
}
 // معالجة طلبات AJAX if (isset($_GET[
    'action'
]) && $_GET[
    'action'
] === 'get_logs') {
 header('Content-Type: application/json');
 $date = $_GET[
    'date'
] ?? date('Y-m-d');
 $level = $_GET[
    'level'
] ?? null;
 $limit = intval($_GET[
    'limit'
] ?? 100);
 try {
 $logs = ErrorHandler::getLogs($date, $level, $limit);
 echo json_encode([
    'success' =>
 true, 'logs' =>
 $logs]);
}
catch (Exception $e) {
 echo json_encode([
    'success' =>
 false, 'error' =>
 'حدث خطأ في جلب السجلات'
]);
}
 exit();
}
 // معالجة تنزيل ملف log if (isset($_GET[
    'action'
]) && $_GET[
    'action'
] === 'download') {
 $date = $_GET[
    'date'
] ?? date('Y-m-d');
 $logFile = __DIR__ . "/logs/error_$date.log";
 if (file_exists($logFile)) {
 header('Content-Type: application/octet-stream');
 header('Content-Disposition: attachment;
 filename="error_log_' . $date . '.log"');
 header('Content-Length: ' . filesize($logFile));
 readfile($logFile);
 exit();
}
else {
 $_SESSION[
    'error'
] = 'ملف السجل غير موجود';
}
}
 require_once __DIR__ . '/includes/admin_header_new.php';
?>
<div class="admin-layout">
<!-- الشريط الجانبي -->
<nav class="modern-sidebar d-none d-lg-block">
<div class="sidebar-section">
<div class="sidebar-section-title">
الإدارة الرئيسية
</div>
<a class="sidebar-nav-link" href="admin_dashboard.php">
<i class="fas fa-tachometer-alt">
</i>
<span>
لوحة التحكم
</span>
</a>
<a class="sidebar-nav-link" href="admin_users.php">
<i class="fas fa-users">
</i>
<span>
إدارة المستخدمين
</span>
</a>
<a class="sidebar-nav-link" href="admin_activity.php">
<i class="fas fa-history">
</i>
<span>
سجل العمليات
</span>
</a>
</div>
<div class="sidebar-section">
<div class="sidebar-section-title">
التقارير والإحصائيات
</div>
<a class="sidebar-nav-link" href="admin_reports.php">
<i class="fas fa-chart-bar">
</i>
<span>
التقارير الشاملة
</span>
</a>
<a class="sidebar-nav-link" href="admin_financial.php">
<i class="fas fa-file-invoice-dollar">
</i>
<span>
التقارير المالية
</span>
</a>
</div>
<div class="sidebar-section">
<div class="sidebar-section-title">
إدارة النظام
</div>
<a class="sidebar-nav-link active" href="admin_error_logs.php">
<i class="fas fa-exclamation-triangle">
</i>
<span>
سجل الأخطاء
</span>
</a>
<a class="sidebar-nav-link" href="admin_system.php">
<i class="fas fa-cogs">
</i>
<span>
إعدادات النظام
</span>
</a>
</div>
<div class="sidebar-section">
<div class="sidebar-section-title">
إدارة المديرين
</div>
<a class="sidebar-nav-link" href="admin_manage_admins.php">
<i class="fas fa-user-shield">
</i>
<span>
إدارة المديرين
</span>
</a>
</div>
</nav>
<!-- المحتوى الرئيسي -->
<main class="admin-content">
<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4 ">
<div>
<h1 class="h2 mb-2 fw-bold gradient-text">
<i class="fas fa-exclamation-triangle me-3">
</i>
 سجل الأخطاء
</h1>
<p class="text-muted mb-0">
مراقبة وتتبع أخطاء النظام والتطبيق
</p>
</div>
<div class="d-flex gap-2">
<button type="button" class="modern-btn modern-btn-secondary" onclick="refreshLogs()">
<i class="fas fa-sync-alt">
</i>
<span>
تحديث
</span>
</button>
<button type="button" class="modern-btn modern-btn-success" onclick="downloadLogs()">
<i class="fas fa-download">
</i>
<span>
تنزيل
</span>
</button>
</div>
</div>
<!-- فلاتر البحث المتطورة -->
<div class="modern-card mb-4">
<div class="modern-card-header">
<h5 class="mb-0 fw-bold">
<i class="fas fa-filter me-2">
</i>
 فلاتر البحث والتصفية
</h5>
</div>
<div class="modern-card-body">
<div class="row g-3">
<div class="col-md-3">
<label for="logDate" class="modern-form-label">
التاريخ
</label>
<input type="date" class="modern-form-control" id="logDate" value="
<?php
 echo date('Y-m-d');
?>
">
</div>
<div class="col-md-3">
<label for="logLevel" class="modern-form-label">
مستوى الخطأ
</label>
<select class="modern-form-control" id="logLevel">
<option value="">
جميع المستويات
</option>
<option value="CRITICAL">
حرج
</option>
<option value="ERROR">
خطأ
</option>
<option value="WARNING">
تحذير
</option>
<option value="DATABASE">
قاعدة البيانات
</option>
<option value="AUTH">
تسجيل الدخول
</option>
<option value="PERMISSION">
الصلاحيات
</option>
<option value="FILE">
الملفات
</option>
<option value="EXCEPTION">
استثناء
</option>
</select>
</div>
<div class="col-md-3">
<label for="logLimit" class="modern-form-label">
عدد السجلات
</label>
<select class="modern-form-control" id="logLimit">
<option value="50">
50
</option>
<option value="100" selected>
100
</option>
<option value="200">
200
</option>
<option value="500">
500
</option>
</select>
</div>
<div class="col-md-3 d-flex align-items-end">
<button type="button" class="modern-btn modern-btn-primary w-100" onclick="loadLogs()">
<i class="fas fa-search">
</i>
<span>
بحث
</span>
</button>
</div>
</div>
</div>
</div>
<!-- إحصائيات سريعة متطورة -->
<div class="row g-4 mb-5">
<!-- أخطاء حرجة -->
<div class="col-xl-3 col-md-6">
<div class="stats-card " >
<div class="d-flex justify-content-between align-items-start">
<div class="flex-1">
<div class="stats-label">
أخطاء حرجة
</div>
<div class="stats-value text-danger" id="criticalCount">
-
</div>
<div class="stats-change text-muted">
<i class="fas fa-exclamation-circle me-1">
</i>
 تحتاج انتباه فوري
</div>
</div>
<div class="stats-icon" style="background: var(--danger-gradient);
">
<i class="fas fa-exclamation-circle">
</i>
</div>
</div>
</div>
</div>
<!-- تحذيرات -->
<div class="col-xl-3 col-md-6">
<div class="stats-card " >
<div class="d-flex justify-content-between align-items-start">
<div class="flex-1">
<div class="stats-label">
تحذيرات
</div>
<div class="stats-value text-warning" id="warningCount">
-
</div>
<div class="stats-change text-muted">
<i class="fas fa-exclamation-triangle me-1">
</i>
 تحذيرات عامة
</div>
</div>
<div class="stats-icon" style="background: var(--warning-gradient);
">
<i class="fas fa-exclamation-triangle">
</i>
</div>
</div>
</div>
</div>
<!-- قاعدة البيانات -->
<div class="col-xl-3 col-md-6">
<div class="stats-card " >
<div class="d-flex justify-content-between align-items-start">
<div class="flex-1">
<div class="stats-label">
قاعدة البيانات
</div>
<div class="stats-value text-info" id="databaseCount">
-
</div>
<div class="stats-change text-muted">
<i class="fas fa-database me-1">
</i>
 أخطاء قاعدة البيانات
</div>
</div>
<div class="stats-icon" style="background: var(--info-gradient);
">
<i class="fas fa-database">
</i>
</div>
</div>
</div>
</div>
<!-- إجمالي اليوم -->
<div class="col-xl-3 col-md-6">
<div class="stats-card " >
<div class="d-flex justify-content-between align-items-start">
<div class="flex-1">
<div class="stats-label">
إجمالي اليوم
</div>
<div class="stats-value" id="totalCount">
-
</div>
<div class="stats-change text-muted">
<i class="fas fa-list me-1">
</i>
 جميع السجلات
</div>
</div>
<div class="stats-icon">
<i class="fas fa-list">
</i>
</div>
</div>
</div>
</div>
</div>
<!-- جدول السجلات المتطور -->
<div class="modern-card " >
<div class="modern-card-header">
<h5 class="mb-0 fw-bold">
<i class="fas fa-list me-2">
</i>
سجل الأخطاء
</h5>
</div>
<div class="modern-card-body">
<div id="loadingSpinner" class="text-center py-5" style="display: none;
">
<div class="spinner-border text-primary" role="status">
<span class="visually-hidden">
جاري التحميل...
</span>
</div>
<p class="mt-3 text-muted">
جاري تحميل السجلات...
</p>
</div>
<div class="table-responsive">
<table class="modern-table table mb-0" id="logsTable">
<thead>
<tr>
<th>
الوقت
</th>
<th>
المستوى
</th>
<th>
الرسالة
</th>
<th>
المستخدم
</th>
<th>
IP
</th>
<th>
الملف
</th>
<th>
الإجراءات
</th>
</tr>
</thead>
<tbody id="logsTableBody">
<tr>
<td colspan="7" class="text-center text-muted py-4">
<i class="fas fa-search fa-2x mb-3 d-block">
</i>
 اضغط على "بحث" لعرض السجلات
</td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
</main>
</div>
<!-- نافذة تفاصيل الخطأ المتطورة -->
<div class="modal fade" id="errorDetailModal" tabindex="-1">
<div class="modal-dialog modal-lg">
<div class="modal-content modern-card">
<div class="modal-header modern-card-header border-0">
<h5 class="modal-title fw-bold gradient-text">
<i class="fas fa-info-circle me-2">
</i>
 تفاصيل الخطأ
</h5>
<button type="button" class="btn-close" data-bs-dismiss="modal">
</button>
</div>
<div class="modal-body modern-card-body">
<div id="errorDetailContent">
</div>
</div>
<div class="modal-footer modern-card-footer border-0">
<button type="button" class="modern-btn modern-btn-secondary" data-bs-dismiss="modal">
<i class="fas fa-times">
</i>
<span>
إغلاق
</span>
</button>
</div>
</div>
</div>
</div>
<script>
 // دالة تحميل السجلات function loadLogs()  {
 const date = document.getElementById('logDate').value;
 const level = document.getElementById('logLevel').value;
 const limit = document.getElementById('logLimit').value;
 const loadingSpinner = document.getElementById('loadingSpinner');
 const logsTableBody = document.getElementById('logsTableBody');
 if (loadingSpinner) loadingSpinner.style.display = 'block';
 if (logsTableBody) logsTableBody.innerHTML = '
<tr>
<td colspan="7" class="text-center">
جاري التحميل...
</td>
</tr>
';
 const url = `admin_error_logs.php?action=get_logs&date=${
encodeURIComponent(date)
}
&level=${
encodeURIComponent(level)
}
&limit=${
encodeURIComponent(limit)
}
`;
 fetch(url, {
 method: 'GET', headers: {
 'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
}
}
) .then(response =>
 {
 if (!response.ok) {
 throw new Error(`HTTP error! status: ${
response.status
}
`);
}
 return response.json();
}
) .then(data =>
 {
 if (loadingSpinner) loadingSpinner.style.display = 'none';
 if (data.success) {
 displayLogs(data.logs);
 updateStatistics(data.logs);
}
else {
 showError(data.error || 'حدث خطأ في تحميل السجلات');
}
}
) .catch(error =>
 {
 console.error('Error loading logs:', error);
 if (loadingSpinner) loadingSpinner.style.display = 'none';
 showError('حدث خطأ في الاتصال: ' + error.message);
}
);
}
 // دالة عرض السجلات function displayLogs(logs)  {
 const tbody = document.getElementById('logsTableBody');
 if (!tbody) {
 console.error('Table body element not found');
 return;
}
 if (!logs || logs.length === 0) {
 tbody.innerHTML = '
<tr>
<td colspan="7" class="text-center text-muted">
لا توجد سجلات للعرض
</td>
</tr>
';
 return;
}
 try {
 tbody.innerHTML = logs.map((log, index) =>
 {
 const levelClass = getLevelClass(log.level);
 const time = formatTimestamp(log.timestamp);
 const fileName = log.file ? log.file.split('/').pop() : '-';
 const message = escapeHtml(log.message || '');
 const userId = escapeHtml(log.user_id || 'غير محدد');
 const ip = escapeHtml(log.ip || '-');
 const line = log.line || '-';
 return `
<tr  >
<td>
<small class="text-muted">
${
time
}
</small>
</td>
<td>
<span class="badge ${
levelClass
}
">
${
log.level
}
</span>
</td>
<td class="text-truncate" style="max-width: 300px;
" title="${
message
}
">
${
message
}
</td>
<td>
<small>
${
userId
}
</small>
</td>
<td>
<small class="text-muted">
${
ip
}
</small>
</td>
<td>
<small class="text-muted">
${
fileName
}
:${
line
}
</small>
</td>
<td>
<button class="modern-btn modern-btn-outline" onclick="showErrorDetail(${
index
}
)" title="عرض التفاصيل">
<i class="fas fa-eye">
</i>
</button>
</td>
</tr>
 `;
}
).join('');
 // حفظ السجلات للاستخدام في التفاصيل window.currentLogs = logs;
}
catch (error) {
 console.error('Error displaying logs:', error);
 tbody.innerHTML = '
<tr>
<td colspan="7" class="text-center text-danger">
حدث خطأ في عرض السجلات
</td>
</tr>
';
}
}
 // دالة مساعدة لتنسيق الوقت function formatTimestamp(timestamp)  {
 try {
 const date = new Date(timestamp);
 return date.toLocaleString('ar-SA', {
 year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit'
}
);
}
catch (error) {
 return timestamp;
}
}
 // دالة مساعدة لتأمين النص function escapeHtml(text)  {
 const div = document.createElement('div');
 div.textContent = text;
 return div.innerHTML;
}
 // دالة تحديث الإحصائيات function updateStatistics(logs)  {
 const stats = {
 critical: 0, warning: 0, database: 0, total: logs.length
}
;
 logs.forEach(log =>
 {
 if ([
    'CRITICAL',
    'ERROR',
    'FATAL'
].includes(log.level)) {
 stats.critical++;
}
else if ([
    'WARNING',
    'NOTICE'
].includes(log.level)) {
 stats.warning++;
}
else if (log.level === 'DATABASE') {
 stats.database++;
}
}
);
 document.getElementById('criticalCount').textContent = stats.critical;
 document.getElementById('warningCount').textContent = stats.warning;
 document.getElementById('databaseCount').textContent = stats.database;
 document.getElementById('totalCount').textContent = stats.total;
}
 // دالة الحصول على فئة المستوى function getLevelClass(level)  {
 const classes = {
 'CRITICAL': 'bg-danger',
    'ERROR': 'bg-danger',
    'FATAL': 'bg-danger',
    'WARNING': 'bg-warning',
    'NOTICE': 'bg-info',
    'DATABASE': 'bg-primary',
    'AUTH': 'bg-secondary',
    'PERMISSION': 'bg-dark',
    'FILE': 'bg-success',
    'EXCEPTION': 'bg-danger'
}
;
 return classes[level] || 'bg-secondary';
}
 // دالة عرض تفاصيل الخطأ function showErrorDetail(logIndex)  {
 if (!window.currentLogs || !window.currentLogs[logIndex]) {
 showError('لا يمكن العثور على تفاصيل هذا السجل');
 return;
}
 const log = window.currentLogs[logIndex];
 try {
 const content = `
<div class="row mb-3">
<div class="col-md-6">
<div class="card border-0 bg-light">
<div class="card-body p-3">
<h6 class="card-title text-primary mb-2">
<i class="fas fa-clock me-2">
</i>
الوقت
</h6>
<p class="card-text">
${
formatTimestamp(log.timestamp)
}
</p>
</div>
</div>
</div>
<div class="col-md-6">
<div class="card border-0 bg-light">
<div class="card-body p-3">
<h6 class="card-title text-primary mb-2">
<i class="fas fa-layer-group me-2">
</i>
المستوى
</h6>
<span class="badge ${
getLevelClass(log.level)
}
 fs-6">
${
log.level
}
</span>
</div>
</div>
</div>
</div>
<div class="card border-0 bg-light mb-3">
<div class="modern-card-body">
<h6 class="card-title text-primary mb-3">
<i class="fas fa-exclamation-circle me-2">
</i>
رسالة الخطأ
</h6>
<div class="alert alert-warning border-0 shadow-sm">
<pre class="mb-0" style="white-space: pre-wrap;
 font-family: inherit;
">
${
escapeHtml(log.message || '')
}
</pre>
</div>
</div>
</div>
<div class="row mb-3">
<div class="col-md-6">
<div class="card border-0 bg-light">
<div class="card-body p-3">
<h6 class="card-title text-primary mb-2">
<i class="fas fa-user me-2">
</i>
المستخدم
</h6>
<p class="card-text">
${
escapeHtml(log.user_id || 'غير محدد')
}
</p>
</div>
</div>
</div>
<div class="col-md-6">
<div class="card border-0 bg-light">
<div class="card-body p-3">
<h6 class="card-title text-primary mb-2">
<i class="fas fa-globe me-2">
</i>
عنوان IP
</h6>
<p class="card-text">
<code>
${
escapeHtml(log.ip || '-')
}
</code>
</p>
</div>
</div>
</div>
</div>
<div class="row mb-3">
<div class="col-md-6">
<div class="card border-0 bg-light">
<div class="card-body p-3">
<h6 class="card-title text-primary mb-2">
<i class="fas fa-file-code me-2">
</i>
الملف
</h6>
<p class="card-text">
<code>
${
escapeHtml(log.file || 'غير محدد')
}
</code>
</p>
</div>
</div>
</div>
<div class="col-md-6">
<div class="card border-0 bg-light">
<div class="card-body p-3">
<h6 class="card-title text-primary mb-2">
<i class="fas fa-hashtag me-2">
</i>
رقم السطر
</h6>
<p class="card-text">
<code>
${
log.line || 'غير محدد'
}
</code>
</p>
</div>
</div>
</div>
</div>
 ${
log.url ? `
<div class="card border-0 bg-light mb-3">
<div class="card-body p-3">
<h6 class="card-title text-primary mb-2">
<i class="fas fa-link me-2">
</i>
الرابط
</h6>
<p class="card-text">
<code>
${
escapeHtml(log.url)
}
</code>
</p>
</div>
</div>
 ` : ''
}
 ${
log.context && Object.keys(log.context).length >
 0 ? `
<div class="card border-0 bg-light">
<div class="modern-card-body">
<h6 class="card-title text-primary mb-3">
<i class="fas fa-info-circle me-2">
</i>
معلومات إضافية
</h6>
<pre class="bg-dark text-light p-3 rounded" style="max-height: 300px;
 overflow-y: auto;
">
${
JSON.stringify(log.context, null, 2)
}
</pre>
</div>
</div>
 ` : ''
}
 `;
 document.getElementById('errorDetailContent').innerHTML = content;
 const modal = new bootstrap.Modal(document.getElementById('errorDetailModal'));
 modal.show();
}
catch (error) {
 console.error('Error showing error detail:', error);
 showError('حدث خطأ في عرض تفاصيل السجل');
}
}
 // دالة تحديث السجلات function refreshLogs()  {
 loadLogs();
}
 // دالة تنزيل السجلات function downloadLogs()  {
 const date = document.getElementById('logDate').value;
 window.open(`admin_error_logs.php?action=download&date=${
date
}
`, '_blank');
}
 // دالة عرض الأخطاء function showError(message)  {
 const tbody = document.getElementById('logsTableBody');
 tbody.innerHTML = `
<tr>
<td colspan="7" class="text-center text-danger">
${
message
}
</td>
</tr>
`;
}
 // تحديث تلقائي كل 30 ثانية setInterval(function() {
 if (document.getElementById('logDate').value === '
<?php
 echo date('Y-m-d');
?>
') {
 loadLogs();
}
}
, 30000);
</script>
<style>
 /* تحسينات إضافية لصفحة سجل الأخطاء */ . {
}
 to {
 opacity: 1;
}
}
 .table-responsive {
 border-radius: var(--admin-border-radius-lg);
 box-shadow: var(--admin-shadow-sm);
}
 .badge {
 font-size: 0.75rem;
 padding: 0.5em 0.75em;
}
 .btn-outline-primary:hover {
 box-shadow: var(--admin-shadow-md);
}
 .modal-content {
 border: none;
 border-radius: var(--admin-border-radius-xl);
 box-shadow: var(--admin-shadow-xl);
}
 .modal-header {
 background: var(--admin-gradient-primary);
 color: white;
 border-radius: var(--admin-border-radius-xl) var(--admin-border-radius-xl) 0 0;
}
 .modal-body .card {
}
 .modal-body .card:hover {
 box-shadow: var(--admin-shadow-md);
}
 /* تحسين الإحصائيات */ .card.text-white {
 border: none;
 box-shadow: var(--admin-shadow-lg);
}
 .card.text-white:hover {
 box-shadow: var(--admin-shadow-xl);
}
 .card.bg-danger {
 background: var(--admin-gradient-danger) !important;
}
 .card.bg-warning {
 background: var(--admin-gradient-warning) !important;
}
 .card.bg-info {
 background: linear-gradient(135deg, var(--admin-cyan) 0%, #0e7490 100%) !important;
}
 .card.bg-secondary {
 background: linear-gradient(135deg, var(--admin-gray) 0%, var(--admin-slate) 100%) !important;
}
 /* تحسين الفلاتر */ .form-control, .form-select {
 border: 2px solid var(--admin-border-color);
 border-radius: var(--admin-border-radius);
}
 .form-control:focus, .form-select:focus {
 border-color: var(--admin-royal-blue);
 box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}
 /* تحسين الأزرار */ .btn-toolbar .btn {
 margin: 0 0.25rem;
}
 .btn i {
 margin-left: 0.5rem;
}
 /* تحسين الجدول */ .table th {
 background: var(--admin-bg-header);
 color: var(--admin-navy);
 font-weight: 600;
 border-bottom: 2px solid var(--admin-border-color);
}
 .table td {
 vertical-align: middle;
 border-bottom: 1px solid rgba(148, 163, 184, 0.15);
}
 .table-hover tbody tr:hover {
 background: rgba(37, 99, 235, 0.05);
}
 /* تحسين الوضع الداكن */ [data-theme="dark"] .table th {
 background: var(--admin-bg-header);
 color: var(--admin-navy);
}
 [data-theme="dark"] .table td {
 background: var(--admin-bg-card);
 color: var(--admin-slate);
}
 [data-theme="dark"] .modal-content {
 background: var(--admin-bg-card);
}
 [data-theme="dark"] .modal-body .card {
 background: var(--admin-bg-header) !important;
}
 [data-theme="dark"] .alert-warning {
 background: rgba(217, 119, 6, 0.15);
 color: var(--admin-amber);
 border-color: rgba(217, 119, 6, 0.3);
}
 /* تحسين الاستجابة */ @media (max-width: 768px) {
 .card-body {
 padding: 1rem;
}
 .btn-toolbar {
 flex-direction: column;
 gap: 0.5rem;
}
 .btn-toolbar .btn {
 width: 100%;
 margin: 0;
}
 .table-responsive {
 font-size: 0.875rem;
}
 .modal-dialog {
 margin: 0.5rem;
}
}
</style>
<!-- تم إزالة الزر المكرر - الزر موجود في admin_header_new.php -->
 // دالة تحديث السجلات function refreshLogs()  {
 loadLogs();
}
 // دالة تنزيل السجلات function downloadLogs()  {
 const date = document.getElementById('logDate').value;
 window.location.href = `admin_error_logs.php?action=download&date=${
encodeURIComponent(date)
}
`;
}
 // دالة عرض رسالة خطأ function showError(message)  {
 const tbody = document.getElementById('logsTableBody');
 if (tbody) {
 tbody.innerHTML = `
<tr>
<td colspan="7" class="text-center text-danger py-4">
<i class="fas fa-exclamation-triangle fa-2x mb-3 d-block">
</i>
 ${
message
}
</td>
</tr>
`;
}
}
 // تحميل الوضع عند تحميل الصفحة document.addEventListener('DOMContentLoaded', function() {
 loadTheme();
 // تحميل السجلات تلقائياً loadLogs();
}
);
</script>
<?php
 require_once __DIR__ . '/includes/admin_footer_new.php';
?>