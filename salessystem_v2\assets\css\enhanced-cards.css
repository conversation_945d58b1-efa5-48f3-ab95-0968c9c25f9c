/* تحسينات متقدمة للبطاقات في قسم المدير */

/* بطاقات متدرجة مع تأثيرات ثلاثية الأبعاد */
.premium-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 24px;
    padding: 2rem;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.5);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    border-radius: 24px 24px 0 0;
}

.premium-card::after {
    content: '';
    position: absolute;
    top: -100%;
    left: -100%;
    width: 300%;
    height: 300%;
    background: conic-gradient(from 0deg, transparent, rgba(102, 126, 234, 0.05), transparent);
    opacity: 0;
    transition: all 0.6s ease;
    pointer-events: none;
}

.premium-card:hover {
    box-shadow: 
        0 30px 60px rgba(0, 0, 0, 0.15),
        0 12px 24px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border-color: rgba(102, 126, 234, 0.3);
}

.premium-card:hover::after {
    opacity: 1;
    animation: shimmer 2s linear infinite;
}

@keyframes shimmer {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* بطاقات إحصائيات فاخرة */
.luxury-stats-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 28px;
    padding: 3rem 2.5rem;
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.12),
        0 10px 20px rgba(0, 0, 0, 0.08),
        inset 0 2px 4px rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.6);
    position: relative;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.luxury-stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    border-radius: 28px 28px 0 0;
}

.luxury-stats-card::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.08) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all 0.5s ease;
    pointer-events: none;
}

.luxury-stats-card:hover {
    box-shadow: 
        0 35px 70px rgba(0, 0, 0, 0.18),
        0 15px 30px rgba(0, 0, 0, 0.12),
        inset 0 2px 4px rgba(255, 255, 255, 1);
    border-color: rgba(102, 126, 234, 0.5);
}

.luxury-stats-card:hover::after {
    opacity: 1;
    width: 300px;
    height: 300px;
}

/* أيقونات فاخرة */
.luxury-icon {
    width: 80px;
    height: 80px;
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    background: linear-gradient(145deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: #667eea;
    margin-bottom: 2rem;
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
        0 8px 16px rgba(102, 126, 234, 0.15),
        inset 0 1px 2px rgba(255, 255, 255, 0.8);
}

.luxury-icon::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
    border-radius: 26px;
    z-index: -1;
    opacity: 0;
    transition: all 0.4s ease;
}

.luxury-stats-card:hover .luxury-icon {
    background: linear-gradient(145deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 
        0 15px 30px rgba(102, 126, 234, 0.4),
        inset 0 1px 2px rgba(255, 255, 255, 0.3);
}

.luxury-stats-card:hover .luxury-icon::before {
    opacity: 1;
}

/* قيم إحصائيات فاخرة */
.luxury-value {
    font-size: 3.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.luxury-label {
    font-size: 1rem;
    font-weight: 700;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    margin-bottom: 1.5rem;
}

.luxury-change {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: #667eea;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

/* الوضع الداكن للبطاقات الفاخرة */
[data-theme="dark"] .premium-card,
[data-theme="dark"] .luxury-stats-card {
    background: linear-gradient(145deg, rgba(26, 32, 44, 0.95) 0%, rgba(45, 55, 72, 0.9) 100%);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.4),
        0 10px 20px rgba(0, 0, 0, 0.3),
        inset 0 1px 2px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .premium-card:hover,
[data-theme="dark"] .luxury-stats-card:hover {
    box-shadow: 
        0 35px 70px rgba(0, 0, 0, 0.5),
        0 15px 30px rgba(0, 0, 0, 0.4),
        inset 0 1px 2px rgba(255, 255, 255, 0.15);
    border-color: rgba(102, 126, 234, 0.6);
}

[data-theme="dark"] .luxury-icon {
    background: linear-gradient(145deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    box-shadow: 
        0 8px 16px rgba(0, 0, 0, 0.3),
        inset 0 1px 2px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .luxury-stats-card:hover .luxury-icon {
    box-shadow: 
        0 15px 30px rgba(102, 126, 234, 0.5),
        inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .luxury-label {
    color: #94a3b8;
}

[data-theme="dark"] .luxury-change {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    color: #a5b4fc;
}

/* تأثيرات إضافية */
.card-glow {
    position: relative;
}

.card-glow::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, #667eea, #764ba2, #f093fb, #f5576c);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    filter: blur(10px);
    transition: all 0.4s ease;
}

.card-glow:hover::before {
    opacity: 0.3;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .premium-card,
    .luxury-stats-card {
        padding: 1.5rem;
        border-radius: 20px;
    }
    
    .luxury-value {
        font-size: 2.5rem;
    }
    
    .luxury-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }
}
