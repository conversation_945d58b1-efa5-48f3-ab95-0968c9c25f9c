<?php
/**
 * صفحة عرض سجل الأخطاء للمدير
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// التحقق من الصلاحيات
if (!hasAdminPermission('view_system_logs')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}

// معالجة طلبات AJAX
if (isset($_GET['action']) && $_GET['action'] === 'get_logs') {
    header('Content-Type: application/json');
    
    $date = $_GET['date'] ?? date('Y-m-d');
    $level = $_GET['level'] ?? null;
    $limit = intval($_GET['limit'] ?? 100);
    
    try {
        $logs = ErrorHandler::getLogs($date, $level, $limit);
        echo json_encode(['success' => true, 'logs' => $logs]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'حدث خطأ في جلب السجلات']);
    }
    exit();
}

// معالجة تنزيل ملف log
if (isset($_GET['action']) && $_GET['action'] === 'download') {
    $date = $_GET['date'] ?? date('Y-m-d');
    $logFile = __DIR__ . "/logs/error_$date.log";
    
    if (file_exists($logFile)) {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="error_log_' . $date . '.log"');
        header('Content-Length: ' . filesize($logFile));
        readfile($logFile);
        exit();
    } else {
        $_SESSION['error'] = 'ملف السجل غير موجود';
    }
}

require_once __DIR__ . '/includes/admin_header.php';
?>

<!-- الشريط العلوي المحسن -->
<nav class="navbar navbar-expand-lg admin-navbar">
    <div class="container-fluid">
        <a class="navbar-brand" href="admin_dashboard.php">
            <i class="fas fa-shield-alt"></i>
            نظام إدارة المبيعات - المدير
        </a>

        <div class="navbar-nav ms-auto">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user-circle"></i>
                    <span><?php echo $_SESSION['admin_username'] ?? 'المدير'; ?></span>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="admin_profile.php"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                    <li><a class="dropdown-item" href="admin_settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="admin_logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                </ul>
            </div>

            <button class="btn btn-outline-secondary btn-sm ms-2" onclick="toggleTheme()" title="تبديل الوضع">
                <i class="fas fa-moon" id="theme-icon"></i>
            </button>
        </div>
    </div>
</nav>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي المحسن -->
        <nav class="sidebar">
            <div class="sidebar-brand">
                <h5><i class="fas fa-shield-alt"></i> لوحة المدير</h5>
            </div>

            <div class="nav-section">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="admin_dashboard.php">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_users.php">
                            <i class="fas fa-users"></i>
                            <span>إدارة المستخدمين</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_activity.php">
                            <i class="fas fa-history"></i>
                            <span>سجل العمليات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="admin_error_logs.php">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>سجل الأخطاء</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_reports.php">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير الشاملة</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                    سجل الأخطاء
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="refreshLogs()">
                        <i class="fas fa-sync-alt me-1"></i>تحديث
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadLogs()">
                        <i class="fas fa-download me-1"></i>تنزيل
                    </button>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <label for="logDate" class="form-label">التاريخ:</label>
                    <input type="date" class="form-control" id="logDate" value="<?php echo date('Y-m-d'); ?>">
                </div>
                <div class="col-md-3">
                    <label for="logLevel" class="form-label">مستوى الخطأ:</label>
                    <select class="form-select" id="logLevel">
                        <option value="">جميع المستويات</option>
                        <option value="CRITICAL">حرج</option>
                        <option value="ERROR">خطأ</option>
                        <option value="WARNING">تحذير</option>
                        <option value="DATABASE">قاعدة البيانات</option>
                        <option value="AUTH">تسجيل الدخول</option>
                        <option value="PERMISSION">الصلاحيات</option>
                        <option value="FILE">الملفات</option>
                        <option value="EXCEPTION">استثناء</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="logLimit" class="form-label">عدد السجلات:</label>
                    <select class="form-select" id="logLimit">
                        <option value="50">50</option>
                        <option value="100" selected>100</option>
                        <option value="200">200</option>
                        <option value="500">500</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="button" class="btn btn-primary" onclick="loadLogs()">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-white bg-danger">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">أخطاء حرجة</h6>
                                    <h4 id="criticalCount">-</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">تحذيرات</h6>
                                    <h4 id="warningCount">-</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">قاعدة البيانات</h6>
                                    <h4 id="databaseCount">-</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-database fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-secondary">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي اليوم</h6>
                                    <h4 id="totalCount">-</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-list fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول السجلات -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>سجل الأخطاء
                    </h6>
                </div>
                <div class="card-body">
                    <div id="loadingSpinner" class="text-center" style="display: none;">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="logsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>الوقت</th>
                                    <th>المستوى</th>
                                    <th>الرسالة</th>
                                    <th>المستخدم</th>
                                    <th>IP</th>
                                    <th>الملف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="logsTableBody">
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        اضغط على "بحث" لعرض السجلات
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نافذة تفاصيل الخطأ -->
<div class="modal fade" id="errorDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الخطأ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="errorDetailContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
// تحميل السجلات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل الوضع المحفوظ
    loadTheme();

    // تحميل السجلات
    loadLogs();
});

// دالة تبديل الوضع الداكن/الفاتح
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('admin-theme', newTheme);

    // تحديث أيقونة الزر
    const themeIcon = document.getElementById('theme-icon');
    if (newTheme === 'dark') {
        themeIcon.className = 'fas fa-sun';
    } else {
        themeIcon.className = 'fas fa-moon';
    }
}

// دالة تحميل الوضع المحفوظ
function loadTheme() {
    const savedTheme = localStorage.getItem('admin-theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);

    const themeIcon = document.getElementById('theme-icon');
    if (savedTheme === 'dark') {
        themeIcon.className = 'fas fa-sun';
    } else {
        themeIcon.className = 'fas fa-moon';
    }
}

// دالة تحميل السجلات
function loadLogs() {
    const date = document.getElementById('logDate').value;
    const level = document.getElementById('logLevel').value;
    const limit = document.getElementById('logLimit').value;

    const loadingSpinner = document.getElementById('loadingSpinner');
    const logsTableBody = document.getElementById('logsTableBody');

    if (loadingSpinner) loadingSpinner.style.display = 'block';
    if (logsTableBody) logsTableBody.innerHTML = '<tr><td colspan="7" class="text-center">جاري التحميل...</td></tr>';

    const url = `admin_error_logs.php?action=get_logs&date=${encodeURIComponent(date)}&level=${encodeURIComponent(level)}&limit=${encodeURIComponent(limit)}`;

    fetch(url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (loadingSpinner) loadingSpinner.style.display = 'none';

        if (data.success) {
            displayLogs(data.logs);
            updateStatistics(data.logs);
        } else {
            showError(data.error || 'حدث خطأ في تحميل السجلات');
        }
    })
    .catch(error => {
        console.error('Error loading logs:', error);
        if (loadingSpinner) loadingSpinner.style.display = 'none';
        showError('حدث خطأ في الاتصال: ' + error.message);
    });
}

// دالة عرض السجلات
function displayLogs(logs) {
    const tbody = document.getElementById('logsTableBody');

    if (!tbody) {
        console.error('Table body element not found');
        return;
    }

    if (!logs || logs.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">لا توجد سجلات للعرض</td></tr>';
        return;
    }

    try {
        tbody.innerHTML = logs.map((log, index) => {
            const levelClass = getLevelClass(log.level);
            const time = formatTimestamp(log.timestamp);
            const fileName = log.file ? log.file.split('/').pop() : '-';
            const message = escapeHtml(log.message || '');
            const userId = escapeHtml(log.user_id || 'غير محدد');
            const ip = escapeHtml(log.ip || '-');
            const line = log.line || '-';

            return `
                <tr class="fade-in" style="animation-delay: ${index * 0.05}s">
                    <td><small class="text-muted">${time}</small></td>
                    <td><span class="badge ${levelClass}">${log.level}</span></td>
                    <td class="text-truncate" style="max-width: 300px;" title="${message}">${message}</td>
                    <td><small>${userId}</small></td>
                    <td><small class="text-muted">${ip}</small></td>
                    <td><small class="text-muted">${fileName}:${line}</small></td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary hover-lift" onclick="showErrorDetail(${index})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');

        // حفظ السجلات للاستخدام في التفاصيل
        window.currentLogs = logs;

    } catch (error) {
        console.error('Error displaying logs:', error);
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">حدث خطأ في عرض السجلات</td></tr>';
    }
}

// دالة مساعدة لتنسيق الوقت
function formatTimestamp(timestamp) {
    try {
        const date = new Date(timestamp);
        return date.toLocaleString('ar-SA', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (error) {
        return timestamp;
    }
}

// دالة مساعدة لتأمين النص
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// دالة تحديث الإحصائيات
function updateStatistics(logs) {
    const stats = {
        critical: 0,
        warning: 0,
        database: 0,
        total: logs.length
    };
    
    logs.forEach(log => {
        if (['CRITICAL', 'ERROR', 'FATAL'].includes(log.level)) {
            stats.critical++;
        } else if (['WARNING', 'NOTICE'].includes(log.level)) {
            stats.warning++;
        } else if (log.level === 'DATABASE') {
            stats.database++;
        }
    });
    
    document.getElementById('criticalCount').textContent = stats.critical;
    document.getElementById('warningCount').textContent = stats.warning;
    document.getElementById('databaseCount').textContent = stats.database;
    document.getElementById('totalCount').textContent = stats.total;
}

// دالة الحصول على فئة المستوى
function getLevelClass(level) {
    const classes = {
        'CRITICAL': 'bg-danger',
        'ERROR': 'bg-danger',
        'FATAL': 'bg-danger',
        'WARNING': 'bg-warning',
        'NOTICE': 'bg-info',
        'DATABASE': 'bg-primary',
        'AUTH': 'bg-secondary',
        'PERMISSION': 'bg-dark',
        'FILE': 'bg-success',
        'EXCEPTION': 'bg-danger'
    };
    return classes[level] || 'bg-secondary';
}

// دالة عرض تفاصيل الخطأ
function showErrorDetail(logIndex) {
    if (!window.currentLogs || !window.currentLogs[logIndex]) {
        showError('لا يمكن العثور على تفاصيل هذا السجل');
        return;
    }

    const log = window.currentLogs[logIndex];

    try {
        const content = `
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="card border-0 bg-light">
                        <div class="card-body p-3">
                            <h6 class="card-title text-primary mb-2">
                                <i class="fas fa-clock me-2"></i>الوقت
                            </h6>
                            <p class="card-text">${formatTimestamp(log.timestamp)}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 bg-light">
                        <div class="card-body p-3">
                            <h6 class="card-title text-primary mb-2">
                                <i class="fas fa-layer-group me-2"></i>المستوى
                            </h6>
                            <span class="badge ${getLevelClass(log.level)} fs-6">${log.level}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card border-0 bg-light mb-3">
                <div class="card-body">
                    <h6 class="card-title text-primary mb-3">
                        <i class="fas fa-exclamation-circle me-2"></i>رسالة الخطأ
                    </h6>
                    <div class="alert alert-warning border-0 shadow-sm">
                        <pre class="mb-0" style="white-space: pre-wrap; font-family: inherit;">${escapeHtml(log.message || '')}</pre>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="card border-0 bg-light">
                        <div class="card-body p-3">
                            <h6 class="card-title text-primary mb-2">
                                <i class="fas fa-user me-2"></i>المستخدم
                            </h6>
                            <p class="card-text">${escapeHtml(log.user_id || 'غير محدد')}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 bg-light">
                        <div class="card-body p-3">
                            <h6 class="card-title text-primary mb-2">
                                <i class="fas fa-globe me-2"></i>عنوان IP
                            </h6>
                            <p class="card-text"><code>${escapeHtml(log.ip || '-')}</code></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="card border-0 bg-light">
                        <div class="card-body p-3">
                            <h6 class="card-title text-primary mb-2">
                                <i class="fas fa-file-code me-2"></i>الملف
                            </h6>
                            <p class="card-text"><code>${escapeHtml(log.file || 'غير محدد')}</code></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 bg-light">
                        <div class="card-body p-3">
                            <h6 class="card-title text-primary mb-2">
                                <i class="fas fa-hashtag me-2"></i>رقم السطر
                            </h6>
                            <p class="card-text"><code>${log.line || 'غير محدد'}</code></p>
                        </div>
                    </div>
                </div>
            </div>

            ${log.url ? `
            <div class="card border-0 bg-light mb-3">
                <div class="card-body p-3">
                    <h6 class="card-title text-primary mb-2">
                        <i class="fas fa-link me-2"></i>الرابط
                    </h6>
                    <p class="card-text"><code>${escapeHtml(log.url)}</code></p>
                </div>
            </div>
            ` : ''}

            ${log.context && Object.keys(log.context).length > 0 ? `
            <div class="card border-0 bg-light">
                <div class="card-body">
                    <h6 class="card-title text-primary mb-3">
                        <i class="fas fa-info-circle me-2"></i>معلومات إضافية
                    </h6>
                    <pre class="bg-dark text-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">${JSON.stringify(log.context, null, 2)}</pre>
                </div>
            </div>
            ` : ''}
        `;

        document.getElementById('errorDetailContent').innerHTML = content;
        const modal = new bootstrap.Modal(document.getElementById('errorDetailModal'));
        modal.show();

    } catch (error) {
        console.error('Error showing error detail:', error);
        showError('حدث خطأ في عرض تفاصيل السجل');
    }
}

// دالة تحديث السجلات
function refreshLogs() {
    loadLogs();
}

// دالة تنزيل السجلات
function downloadLogs() {
    const date = document.getElementById('logDate').value;
    window.open(`admin_error_logs.php?action=download&date=${date}`, '_blank');
}

// دالة عرض الأخطاء
function showError(message) {
    const tbody = document.getElementById('logsTableBody');
    tbody.innerHTML = `<tr><td colspan="7" class="text-center text-danger">${message}</td></tr>`;
}

// تحديث تلقائي كل 30 ثانية
setInterval(function() {
    if (document.getElementById('logDate').value === '<?php echo date('Y-m-d'); ?>') {
        loadLogs();
    }
}, 30000);
</script>

<style>
/* تحسينات إضافية لصفحة سجل الأخطاء */
.fade-in {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.table-responsive {
    border-radius: var(--admin-border-radius-lg);
    box-shadow: var(--admin-shadow-sm);
}

.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
}

.btn-outline-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--admin-shadow-md);
}

.modal-content {
    border: none;
    border-radius: var(--admin-border-radius-xl);
    box-shadow: var(--admin-shadow-xl);
}

.modal-header {
    background: var(--admin-gradient-primary);
    color: white;
    border-radius: var(--admin-border-radius-xl) var(--admin-border-radius-xl) 0 0;
}

.modal-body .card {
    transition: var(--admin-transition);
}

.modal-body .card:hover {
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-md);
}

/* تحسين الإحصائيات */
.card.text-white {
    border: none;
    box-shadow: var(--admin-shadow-lg);
    transition: var(--admin-transition);
}

.card.text-white:hover {
    transform: translateY(-4px);
    box-shadow: var(--admin-shadow-xl);
}

.card.bg-danger {
    background: var(--admin-gradient-danger) !important;
}

.card.bg-warning {
    background: var(--admin-gradient-warning) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, var(--admin-cyan) 0%, #0e7490 100%) !important;
}

.card.bg-secondary {
    background: linear-gradient(135deg, var(--admin-gray) 0%, var(--admin-slate) 100%) !important;
}

/* تحسين الفلاتر */
.form-control, .form-select {
    border: 2px solid var(--admin-border-color);
    border-radius: var(--admin-border-radius);
    transition: var(--admin-transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--admin-royal-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* تحسين الأزرار */
.btn-toolbar .btn {
    margin: 0 0.25rem;
}

.btn i {
    margin-left: 0.5rem;
}

/* تحسين الجدول */
.table th {
    background: var(--admin-bg-header);
    color: var(--admin-navy);
    font-weight: 600;
    border-bottom: 2px solid var(--admin-border-color);
}

.table td {
    vertical-align: middle;
    border-bottom: 1px solid rgba(148, 163, 184, 0.15);
}

.table-hover tbody tr:hover {
    background: rgba(37, 99, 235, 0.05);
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* تحسين الوضع الداكن */
[data-theme="dark"] .table th {
    background: var(--admin-bg-header);
    color: var(--admin-navy);
}

[data-theme="dark"] .table td {
    background: var(--admin-bg-card);
    color: var(--admin-slate);
}

[data-theme="dark"] .modal-content {
    background: var(--admin-bg-card);
}

[data-theme="dark"] .modal-body .card {
    background: var(--admin-bg-header) !important;
}

[data-theme="dark"] .alert-warning {
    background: rgba(217, 119, 6, 0.15);
    color: var(--admin-amber);
    border-color: rgba(217, 119, 6, 0.3);
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .btn-toolbar {
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn-toolbar .btn {
        width: 100%;
        margin: 0;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }
}
</style>

<?php require_once __DIR__ . '/includes/admin_footer.php'; ?>
