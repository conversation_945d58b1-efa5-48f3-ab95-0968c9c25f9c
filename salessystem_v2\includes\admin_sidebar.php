<?php
/**
 * القائمة الجانبية الموحدة لقسم المدير
 * نفس العناصر والألوان في جميع الصفحات
 */

// تحديد الصفحة الحالية لتمييز الرابط النشط
$current_page = basename($_SERVER['PHP_SELF']);

// دالة مساعدة للترجمة (إذا لم تكن موجودة)
if (!function_exists('__')) {
    function __($key) {
        $translations = [
            'admin_panel' => 'لوحة المدير',
            'main_management' => 'الإدارة الرئيسية',
            'dashboard' => 'لوحة التحكم',
            'manage_users' => 'إدارة المستخدمين',
            'activity_log' => 'سجل العمليات',
            'reports_statistics' => 'التقارير والإحصائيات',
            'comprehensive_reports' => 'التقارير الشاملة',
            'financial_reports' => 'التقارير المالية',
            'system_management' => 'إدارة النظام',
            'error_logs' => 'سجل الأخطاء',
            'system_settings' => 'إعدادات النظام',
            'diagnostic_tools' => 'أدوات التشخيص',
            'system_status' => 'حالة النظام',
            'translation_status' => 'حالة الترجمة',
            'admin_management' => 'إدارة المديرين',
            'manage_admins' => 'إدارة المديرين'
        ];
        return $translations[$key] ?? $key;
    }
}

// دالة مساعدة للصلاحيات (إذا لم تكن موجودة)
if (!function_exists('hasAdminPermission')) {
    function hasAdminPermission($permission) {
        return isset($_SESSION['admin_is_super']) && $_SESSION['admin_is_super'] === true;
    }
}
?>

<!-- الشريط الجانبي الموحد -->
<nav class="col-md-3 col-lg-2 d-md-block sidebar">
    <div class="position-sticky pt-3">
        <!-- عنوان القسم -->
        <div class="sidebar-brand">
            <h5><i class="fas fa-shield-alt me-2"></i><?php echo __('admin_panel'); ?></h5>
        </div>

        <ul class="nav flex-column">
            <!-- القسم الرئيسي -->
            <li class="nav-section">
                <div class="nav-section-title"><?php echo __('main_management'); ?></div>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'admin_dashboard.php') ? 'active' : ''; ?>" href="admin_dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span><?php echo __('dashboard'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'admin_users.php') ? 'active' : ''; ?>" href="admin_users.php">
                    <i class="fas fa-users"></i>
                    <span><?php echo __('manage_users'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'admin_activity.php') ? 'active' : ''; ?>" href="admin_activity.php">
                    <i class="fas fa-history"></i>
                    <span><?php echo __('activity_log'); ?></span>
                </a>
            </li>

            <!-- قسم التقارير -->
            <li class="nav-section">
                <div class="nav-section-title"><?php echo __('reports_statistics'); ?></div>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'admin_reports.php') ? 'active' : ''; ?>" href="admin_reports.php">
                    <i class="fas fa-chart-bar"></i>
                    <span><?php echo __('comprehensive_reports'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'admin_financial_reports.php') ? 'active' : ''; ?>" href="admin_financial_reports.php">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <span><?php echo __('financial_reports'); ?></span>
                </a>
            </li>

            <!-- قسم النظام -->
            <li class="nav-section">
                <div class="nav-section-title"><?php echo __('system_management'); ?></div>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'admin_error_logs.php') ? 'active' : ''; ?>" href="admin_error_logs.php">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span><?php echo __('error_logs'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'admin_system.php') ? 'active' : ''; ?>" href="admin_system.php">
                    <i class="fas fa-cogs"></i>
                    <span><?php echo __('system_settings'); ?></span>
                </a>
            </li>

            <!-- أدوات التشخيص -->
            <li class="nav-section">
                <div class="nav-section-title"><?php echo __('diagnostic_tools'); ?></div>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'system_status.php') ? 'active' : ''; ?>" href="system_status.php">
                    <i class="fas fa-heartbeat"></i>
                    <span><?php echo __('system_status'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'translation_summary.php') ? 'active' : ''; ?>" href="translation_summary.php">
                    <i class="fas fa-language"></i>
                    <span><?php echo __('translation_status'); ?></span>
                </a>
            </li>

            <!-- إدارة المديرين (للمدير الرئيسي فقط) -->
            <?php if (hasAdminPermission('manage_admins')): ?>
            <li class="nav-section">
                <div class="nav-section-title"><?php echo __('admin_management'); ?></div>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'admin_manage_admins.php') ? 'active' : ''; ?>" href="admin_manage_admins.php">
                    <i class="fas fa-user-shield"></i>
                    <span><?php echo __('manage_admins'); ?></span>
                </a>
            </li>
            <?php endif; ?>
        </ul>
    </div>
</nav>
