<?php
/**
 * صفحة تفاصيل المستخدم للمدير - محدثة
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// التحقق من الصلاحيات
if (!hasAdminPermission('manage_users')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}

$user_id = intval($_GET['id'] ?? 0);
if ($user_id <= 0) {
    $_SESSION['error'] = 'معرف المستخدم غير صحيح';
    header("Location: admin_users.php");
    exit();
}

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    // جلب بيانات المستخدم
    $user_query = "SELECT * FROM users WHERE id = ?";
    $user_stmt = $db->prepare($user_query);
    if (!$user_stmt) {
        ErrorHandler::logDatabaseError($user_query, $db->error);
        throw new Exception("خطأ في إعداد استعلام المستخدم");
    }

    $user_stmt->bind_param("i", $user_id);
    if (!$user_stmt->execute()) {
        ErrorHandler::logDatabaseError($user_query, $user_stmt->error);
        throw new Exception("خطأ في تنفيذ استعلام المستخدم");
    }

    $user_result = $user_stmt->get_result();
    $user_info = $user_result->fetch_assoc();
    $user_stmt->close();

    if (!$user_info) {
        $_SESSION['error'] = 'المستخدم غير موجود';
        header("Location: admin_users.php");
        exit();
    }

    // إحصائيات المستخدم من النظام الموحد
    $stats_query = "SELECT
        (SELECT COUNT(*) FROM sales WHERE user_id = ?) as sales_count,
        (SELECT COUNT(*) FROM purchases WHERE user_id = ?) as purchases_count,
        (SELECT COUNT(*) FROM customers WHERE user_id = ?) as customers_count,
        (SELECT COALESCE(SUM(total_amount), 0) FROM sales WHERE user_id = ?) as total_sales_amount,
        (SELECT COALESCE(SUM(total_amount), 0) FROM purchases WHERE user_id = ?) as total_purchases_amount";

    $stats_stmt = $db->prepare($stats_query);
    if ($stats_stmt) {
        $stats_stmt->bind_param("iiiii", $user_id, $user_id, $user_id, $user_id, $user_id);
        $stats_stmt->execute();
        $user_stats = $stats_stmt->get_result()->fetch_assoc();
        $stats_stmt->close();
    } else {
        ErrorHandler::logDatabaseError($stats_query, $db->error);
        $user_stats = [
            'sales_count' => 0,
            'purchases_count' => 0,
            'customers_count' => 0,
            'total_sales_amount' => 0,
            'total_purchases_amount' => 0
        ];
    }

    // إحصائيات نشاط المستخدم
    $activity_stats_query = "SELECT
        COUNT(*) as total_activities,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_activities,
        COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_activities,
        COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as month_activities
        FROM activity_log
        WHERE user_id = ? AND user_type = 'user'";

    $activity_stats_stmt = $db->prepare($activity_stats_query);
    if ($activity_stats_stmt) {
        $activity_stats_stmt->bind_param("i", $user_id);
        $activity_stats_stmt->execute();
        $activity_stats = $activity_stats_stmt->get_result()->fetch_assoc();
        $activity_stats_stmt->close();
    } else {
        ErrorHandler::logDatabaseError($activity_stats_query, $db->error);
        $activity_stats = [
            'total_activities' => 0,
            'today_activities' => 0,
            'week_activities' => 0,
            'month_activities' => 0
        ];
    }

    // أحدث العمليات
    $activities_query = "SELECT * FROM activity_log WHERE user_id = ? AND user_type = 'user' ORDER BY created_at DESC LIMIT 15";
    $activities_stmt = $db->prepare($activities_query);
    $recent_activities = [];
    if ($activities_stmt) {
        $activities_stmt->bind_param("i", $user_id);
        $activities_stmt->execute();
        $activities_result = $activities_stmt->get_result();
        while ($row = $activities_result->fetch_assoc()) {
            $recent_activities[] = $row;
        }
        $activities_stmt->close();
    }

    // أحدث المبيعات
    $recent_sales_query = "SELECT s.*, c.name as customer_name FROM sales s
                          LEFT JOIN customers c ON s.customer_id = c.id
                          WHERE s.user_id = ? ORDER BY s.created_at DESC LIMIT 5";
    $recent_sales_stmt = $db->prepare($recent_sales_query);
    $recent_sales = [];
    if ($recent_sales_stmt) {
        $recent_sales_stmt->bind_param("i", $user_id);
        $recent_sales_stmt->execute();
        $recent_sales_result = $recent_sales_stmt->get_result();
        while ($row = $recent_sales_result->fetch_assoc()) {
            $recent_sales[] = $row;
        }
        $recent_sales_stmt->close();
    }

} catch (Exception $e) {
    ErrorHandler::logError('ERROR', 'Admin user details error: ' . $e->getMessage(), __FILE__, __LINE__, ['user_id' => $user_id]);
    $_SESSION['error'] = 'حدث خطأ في تحميل بيانات المستخدم: ' . $e->getMessage();

    // قيم افتراضية في حالة الخطأ
    $user_stats = ['sales_count' => 0, 'purchases_count' => 0, 'customers_count' => 0, 'total_sales_amount' => 0, 'total_purchases_amount' => 0];
    $activity_stats = ['total_activities' => 0, 'today_activities' => 0, 'week_activities' => 0, 'month_activities' => 0];
    $recent_activities = [];
    $recent_sales = [];
}

require_once __DIR__ . '/includes/admin_header_new.php';
?>

<div class="admin-layout">
        <!-- الشريط الجانبي -->
        <nav class="modern-sidebar d-none d-lg-block"><div class="sidebar-section"><div class="sidebar-section-title">الإدارة الرئيسية</div><a class="sidebar-nav-link" href="admin_dashboard.php"><i class="fas fa-tachometer-alt"></i><span>لوحة التحكم</span></a><a class="sidebar-nav-link" href="admin_users.php"><i class="fas fa-users"></i><span>إدارة المستخدمين</span></a><a class="sidebar-nav-link" href="admin_activity.php"><i class="fas fa-history"></i><span>سجل العمليات</span></a></div><div class="sidebar-section"><div class="sidebar-section-title">التقارير والإحصائيات</div><a class="sidebar-nav-link" href="admin_reports.php"><i class="fas fa-chart-bar"></i><span>التقارير الشاملة</span></a><a class="sidebar-nav-link" href="admin_financial.php"><i class="fas fa-file-invoice-dollar"></i><span>التقارير المالية</span></a></div><div class="sidebar-section"><div class="sidebar-section-title">إدارة النظام</div><a class="sidebar-nav-link" href="admin_error_logs.php"><i class="fas fa-exclamation-triangle"></i><span>سجل الأخطاء</span></a><a class="sidebar-nav-link" href="admin_system.php"><i class="fas fa-cogs"></i><span>إعدادات النظام</span></a></div><div class="sidebar-section"><div class="sidebar-section-title">إدارة المديرين</div><a class="sidebar-nav-link" href="admin_manage_admins.php"><i class="fas fa-user-shield"></i><span>إدارة المديرين</span></a></div></nav>

        <!-- المحتوى الرئيسي -->
        <main class="admin-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-user me-2 text-primary"></i>تفاصيل المستخدم
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="admin_users.php" class="modern-btn modern-btn-sm btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>العودة للمستخدمين
                    </a>
                    <div class="btn-group me-2">
                        <a href="admin_invoice_details.php?user_id=<?php echo $user_id; ?>" class="modern-btn modern-btn-sm btn-info">
                            <i class="fas fa-file-invoice me-1"></i>عرض الفواتير
                        </a>
                    </div>
                </div>
            </div>

            <!-- معلومات المستخدم الأساسية -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-user-circle me-2"></i>المعلومات الشخصية
                            </h6>
                        </div>
                        <div class="modern-card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الاسم الكامل</label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($user_info['full_name']); ?></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">اسم المستخدم</label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($user_info['username']); ?></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted"><?php echo t("email"); ?></label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($user_info['email']); ?></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">رقم الهاتف</label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($user_info['phone'] ?? 'غير محدد'); ?></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">تاريخ التسجيل</label>
                                    <div class="fw-bold"><?php echo date('Y-m-d H:i', strtotime($user_info['created_at'])); ?></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">آخر تسجيل دخول</label>
                                    <div class="fw-bold">
                                        <?php echo $user_info['last_login'] ? date('Y-m-d H:i', strtotime($user_info['last_login'])) : 'لم يسجل دخول بعد'; ?>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">حالة الحساب</label>
                                    <div>
                                        <span class="badge <?php echo $user_info['status'] === 'active' ? 'bg-success' : 'bg-danger'; ?>">
                                            <?php echo $user_info['status'] === 'active' ? t("active") : t("inactive"); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">اللغة المفضلة</label>
                                    <div class="fw-bold"><?php echo $user_info['preferred_language'] === 'ar' ? 'العربية' : 'الإنجليزية'; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>إحصائيات سريعة
                            </h6>
                        </div>
                        <div class="modern-card-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>إجمالي النشاط</span>
                                    <strong><?php echo number_format($activity_stats['total_activities']); ?></strong>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>نشاط اليوم</span>
                                    <strong><?php echo number_format($activity_stats['today_activities']); ?></strong>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>نشاط الأسبوع</span>
                                    <strong><?php echo number_format($activity_stats['week_activities']); ?></strong>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>نشاط الشهر</span>
                                    <strong><?php echo number_format($activity_stats['month_activities']); ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات البيانات -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="modern-card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        فواتير المبيعات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($user_stats['sales_count']); ?>
                                    </div>
                                    <div class="text-xs text-muted">
                                        <?php echo number_format($user_stats['total_sales_amount'], 2); ?> ر.س
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="modern-card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        فواتير المشتريات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($user_stats['purchases_count']); ?>
                                    </div>
                                    <div class="text-xs text-muted">
                                        <?php echo number_format($user_stats['total_purchases_amount'], 2); ?> ر.س
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-truck fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="modern-card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        العملاء
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($user_stats['customers_count']); ?>
                                    </div>
                                    <div class="text-xs text-muted">
                                        عميل مسجل
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-friends fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="modern-card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        صافي الربح
                                    </div>
                                    <?php $profit = $user_stats['total_sales_amount'] - $user_stats['total_purchases_amount']; ?>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($profit, 2); ?> ر.س
                                    </div>
                                    <div class="text-xs text-muted">
                                        <?php echo $profit >= 0 ? 'ربح' : 'خسارة'; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أحدث المبيعات -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="modern-card hover-lift fade-in">
                        <div class="modern-card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-shopping-cart me-2"></i>أحدث المبيعات
                            </h6>
                        </div>
                        <div class="modern-card-body">
                            <?php if (!empty($recent_sales)): ?>
                                <div class="modern-table table-responsive">
                                    <table class="modern-table table table-hover">
                                        <thead>
                                            <tr>
                                                <th>رقم الفاتورة</th>
                                                <th>العميل</th>
                                                <th><?php echo t("date"); ?></th>
                                                <th>المبلغ</th>
                                                <th><?php echo t("status"); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_sales as $sale): ?>
                                            <tr>
                                                <td>
                                                    <span class="modern-badge modern-badge-primary"><?php echo htmlspecialchars($sale['invoice_number']); ?></span>
                                                </td>
                                                <td><?php echo htmlspecialchars($sale['customer_name'] ?? 'عميل غير محدد'); ?></td>
                                                <td>
                                                    <small><?php echo date('Y-m-d', strtotime($sale['created_at'])); ?></small>
                                                </td>
                                                <td>
                                                    <strong><?php echo number_format($sale['total_amount'], 2); ?> ر.س</strong>
                                                </td>
                                                <td>
                                                    <span class="badge <?php echo $sale['payment_status'] === 'paid' ? 'bg-success' : 'bg-warning'; ?>">
                                                        <?php echo $sale['payment_status'] === 'paid' ? t("paid") : t("unpaid"); ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لا توجد مبيعات مسجلة</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أحدث الأنشطة -->
            <div class="modern-card hover-lift fade-in">
                <div class="modern-card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-history me-2"></i>أحدث الأنشطة
                    </h6>
                </div>
                <div class="modern-card-body">
                    <div class="modern-table table-responsive">
                        <table class="modern-table table table-bordered table-hover" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>العملية</th>
                                    <th>الجدول</th>
                                    <th>الوصف</th>
                                    <th>IP Address</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($recent_activities)): ?>
                                    <?php foreach ($recent_activities as $activity): ?>
                                    <tr>
                                        <td>
                                            <small><?php echo date('Y-m-d H:i:s', strtotime($activity['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <?php
                                            $action_icons = [
                                                'user_login' => 'fas fa-sign-in-alt text-success',
                                                'user_logout' => 'fas fa-sign-out-alt text-warning',
                                                'create' => 'fas fa-plus text-success',
                                                'update' => 'fas fa-edit text-primary',
                                                'delete' => 'fas fa-trash text-danger',
                                                'view' => 'fas fa-eye text-info'
                                            ];
                                            $icon = $action_icons[$activity['action']] ?? 'fas fa-circle text-secondary';
                                            ?>
                                            <i class="<?php echo $icon; ?> me-1"></i>
                                            <code><?php echo htmlspecialchars($activity['action']); ?></code>
                                        </td>
                                        <td>
                                            <?php if (!empty($activity['table_name'])): ?>
                                                <span class="modern-badge modern-badge-secondary"><?php echo htmlspecialchars($activity['table_name']); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars($activity['description'] ?? 'لا يوجد وصف'); ?></small>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo htmlspecialchars($activity['ip_address'] ?? '-'); ?></small>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-history fa-2x mb-2"></i>
                                                <p>لا توجد أنشطة مسجلة لهذا المستخدم</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </main></div>

<!-- CSS مخصص -->
<style>
.avatar-lg {
    width: 80px;
    height: 80px;
}

.avatar-initial {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 24px;
    color: white;
}

.stats-card {
    background: var(--admin-bg-card);
    border-radius: var(--admin-border-radius-lg);
    box-shadow: var(--admin-shadow-sm);
    transition: var(--admin-transition);
    border: 1px solid var(--admin-border-color);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--admin-gradient-primary);
    opacity: 0;
    transition: var(--admin-transition);
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-lg);
}

.stats-card:hover::before {
    opacity: 1;
}

.border-left-primary {
    border-left: 4px solid var(--admin-royal-blue) !important;
}

.border-left-success {
    border-left: 4px solid var(--admin-emerald) !important;
}

.border-left-info {
    border-left: 4px solid var(--admin-cyan) !important;
}

.border-left-warning {
    border-left: 4px solid var(--admin-amber) !important;
}

.table th {
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--admin-slate);
    border-bottom: 2px solid var(--admin-border-color);
    padding: 12px 8px;
}

.table td {
    padding: 12px 8px;
    border-bottom: 1px solid var(--admin-border-light);
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: var(--admin-light);
}

.badge {
    font-size: 10px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 6px;
}

.card-header {
    background: var(--admin-bg-header);
    border-bottom: 1px solid var(--admin-border-color);
    font-weight: 600;
}

/* تحسين الوضع الداكن */
[data-theme="dark"] .stats-card {
    background: var(--admin-bg-card);
    border-color: var(--admin-border-color);
}

[data-theme="dark"] .table th {
    color: var(--admin-slate);
    border-bottom-color: var(--admin-border-color);
}

[data-theme="dark"] .table td {
    border-bottom-color: var(--admin-border-light);
}

[data-theme="dark"] .table tbody tr:hover {
    background-color: var(--admin-border-light);
}

[data-theme="dark"] .avatar-initial {
    background: var(--admin-gradient-primary);
}

/* تحسين البطاقات */
.card {
    border: 1px solid var(--admin-border-color);
    border-radius: var(--admin-border-radius-lg);
    box-shadow: var(--admin-shadow-sm);
    transition: var(--admin-transition);
}

.card:hover {
    box-shadow: var(--admin-shadow-md);
}

/* تحسين الأزرار */
.btn {
    border-radius: var(--admin-border-radius);
    font-weight: 500;
    transition: var(--admin-transition);
}

.btn:hover {
    transform: translateY(-1px);
}

/* تحسين الشارات */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--admin-gray);
}

.breadcrumb-item a {
    color: var(--admin-royal-blue);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: var(--admin-slate);
}
</style>

<script>
// تعديل بيانات المستخدم
function editUser(userId) {
    Swal.fire({
        title: 'تعديل بيانات المستخدم',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label class="modern-form-label">الاسم الكامل</label>
                    <input type="text" id="edit_full_name" class="modern-form-control" value="<?php echo htmlspecialchars($user_info['full_name'] ?? ''); ?>">
                </div>
                <div class="mb-3">
                    <label class="modern-form-label"><?php echo t("email"); ?></label>
                    <input type="email" id="edit_email" class="modern-form-control" value="<?php echo htmlspecialchars($user_info['email'] ?? ''); ?>">
                </div>
                <div class="mb-3">
                    <label class="modern-form-label">رقم الهاتف</label>
                    <input type="text" id="edit_phone" class="modern-form-control" value="<?php echo htmlspecialchars($user_info['phone'] ?? ''); ?>">
                </div>
                <div class="mb-3">
                    <label class="modern-form-label">حالة الحساب</label>
                    <select id="edit_status" class="form-select">
                        <option value="1" ${<?php echo $user_info['is_active'] ? 'true' : 'false'; ?> ? 'selected' : ''}><?php echo t("active"); ?></option>
                        <option value="0" ${<?php echo !$user_info['is_active'] ? 'true' : 'false'; ?> ? 'selected' : ''}><?php echo t("inactive"); ?></option>
                    </select>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'حفظ التغييرات',
        cancelButtonText: t("cancel"),
        preConfirm: () => {
            const fullName = document.getElementById('edit_full_name').value;
            const email = document.getElementById('edit_email').value;
            const phone = document.getElementById('edit_phone').value;
            const status = document.getElementById('edit_status').value;

            if (!fullName.trim()) {
                Swal.showValidationMessage('يرجى إدخال الاسم الكامل');
                return false;
            }

            if (!email.trim()) {
                Swal.showValidationMessage('يرجى إدخال البريد الإلكتروني');
                return false;
            }

            return { fullName, email, phone, status };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // إرسال البيانات للخادم
            const formData = new FormData();
            formData.append('action', 'update_user');
            formData.append('user_id', userId);
            formData.append('full_name', result.value.fullName);
            formData.append('email', result.value.email);
            formData.append('phone', result.value.phone);
            formData.append('is_active', result.value.status);

            fetch('admin_user_actions.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم!', 'تم تحديث بيانات المستخدم بنجاح', 'success')
                        .then(() => location.reload());
                } else {
                    Swal.fire('خطأ!', data.message || 'حدث خطأ أثناء التحديث', 'error');
                }
            })
            .catch(error => {
                Swal.fire('خطأ!', 'حدث خطأ في الاتصال', 'error');
            });
        }
    });
}

// إعادة تعيين كلمة المرور
function resetPassword(userId) {
    Swal.fire({
        title: 'إعادة تعيين كلمة المرور',
        text: 'هل تريد إعادة تعيين كلمة مرور هذا المستخدم؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، أعد التعيين',
        cancelButtonText: t("cancel")
    }).then((result) => {
        if (result.isConfirmed) {
            const formData = new FormData();
            formData.append('action', 'reset_password');
            formData.append('user_id', userId);

            fetch('admin_user_actions.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'تم!',
                        html: `تم إعادة تعيين كلمة المرور بنجاح<br><strong>كلمة المرور الجديدة: ${data.new_password}</strong>`,
                        icon: 'success'
                    });
                } else {
                    Swal.fire('خطأ!', data.message || 'حدث خطأ أثناء إعادة التعيين', 'error');
                }
            })
            .catch(error => {
                Swal.fire('خطأ!', 'حدث خطأ في الاتصال', 'error');
            });
        }
    });
}

// تحديث تلقائي للإحصائيات كل دقيقتين
setInterval(function() {
    fetch(`admin_user_stats.php?user_id=<?php echo $user_id; ?>`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث الإحصائيات في الصفحة
                updateStats(data.stats);
            }
        })
        .catch(error => console.log('Error updating stats:', error));
}, 120000); // دقيقتان

function updateStats(stats) {
    // تحديث قيم الإحصائيات في الصفحة
    // يمكن تطوير هذه الدالة لتحديث القيم بدون إعادة تحميل الصفحة
}
</script>

<!-- تضمين SweetAlert2 للتنبيهات المحسنة -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<?php require_once __DIR__ . '/includes/admin_footer_new.php'; ?>
