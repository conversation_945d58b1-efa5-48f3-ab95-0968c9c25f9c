<?php
/**
 * صفحة التقارير الشاملة للمدير
 */
require_once __DIR__ . '/config/init.php';
// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}
// التحقق من الصلاحيات
if (!hasAdminPermission('view_reports')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}
global $main_db;
// معاملات التقرير
$report_type = $_GET['type'] ?? 'overview';
$date_from = $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_GET['date_to'] ?? date('Y-m-d');
// جلب إحصائيات شاملة
$overview_stats = [];
// إحصائيات المستخدمين
$user_stats_query = "SELECT
    COUNT(*) as total_users,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_month
    FROM users";
$user_stats_result = $main_db->query($user_stats_query);
if (!$user_stats_result) {
    ErrorHandler::logDatabaseError($user_stats_query, $main_db->error);
    $user_stats = [
    'total_users' => 0, 'active_users' => 0, 'new_users_month' => 0];
}
else {
    $user_stats = $user_stats_result->fetch_assoc();
}
// إحصائيات النشاط
$activity_stats = $main_db->query("SELECT
    COUNT(*) as total_activities,
    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_activities,
    COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_activities
    FROM activity_log")->fetch_assoc();
// إحصائيات النظام الموحد
$db_stats = [];
// جلب إحصائيات من قاعدة البيانات الموحدة
$sales_count = $main_db->query("SELECT COUNT(*) as count FROM sales")->fetch_assoc()['count'] ?? 0;
$purchases_count = $main_db->query("SELECT COUNT(*) as count FROM purchases")->fetch_assoc()['count'] ?? 0;
$customers_count = $main_db->query("SELECT COUNT(*) as count FROM customers")->fetch_assoc()['count'] ?? 0;
// إحصائيات لكل مستخدم
$users_result = $main_db->query("SELECT id FROM users WHERE status = 'active'");
while ($user = $users_result->fetch_assoc()) {
    $user_id = $user['id'];
    // جلب إحصائيات المستخدم من قاعدة البيانات الموحدة
    $user_sales = $main_db->query("SELECT COUNT(*) as count FROM sales WHERE user_id = $user_id")->fetch_assoc()['count'] ?? 0;
    $user_purchases = $main_db->query("SELECT COUNT(*) as count FROM purchases WHERE user_id = $user_id")->fetch_assoc()['count'] ?? 0;
    $user_customers = $main_db->query("SELECT COUNT(*) as count FROM customers WHERE user_id = $user_id")->fetch_assoc()['count'] ?? 0;
    $db_stats[] = [
    'user_id' => $user_id,
        'sales_count' => $user_sales,
        'purchases_count' => $user_purchases,
        'customers_count' => $user_customers
    ];
}
// حساب المجاميع
$total_sales = $sales_count;
$total_purchases = $purchases_count;
$total_customers = $customers_count;
require_once __DIR__ . '/includes/admin_header_new.php';
?>
<div class="admin-layout">
        <!-- الشريط الجانبي -->
        <nav class="modern-sidebar d-none d-lg-block">
            <div class="sidebar-section">
                <div class="sidebar-section-title">الإدارة الرئيسية</div>
                <a class="sidebar-nav-link" href="admin_dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
                <a class="sidebar-nav-link" href="admin_users.php">
                    <i class="fas fa-users"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                <a class="sidebar-nav-link" href="admin_activity.php">
                    <i class="fas fa-history"></i>
                    <span>سجل العمليات</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">التقارير والإحصائيات</div>
                <a class="sidebar-nav-link" href="admin_reports.php">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير الشاملة</span>
                </a>
                <a class="sidebar-nav-link" href="admin_financial.php">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <span>التقارير المالية</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة النظام</div>
                <a class="sidebar-nav-link" href="admin_error_logs.php">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>سجل الأخطاء</span>
                </a>
                <a class="sidebar-nav-link" href="admin_system.php">
                    <i class="fas fa-cogs"></i>
                    <span>إعدادات النظام</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة المديرين</div>
                <a class="sidebar-nav-link" href="admin_manage_admins.php">
                    <i class="fas fa-user-shield"></i>
                    <span>إدارة المديرين</span>
                </a>
            </div>
        </nav>
        <!-- المحتوى الرئيسي -->
        <main class="admin-content">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-2 fw-bold gradient-text">
                        <i class="fas fa-chart-bar me-3"></i>
                        التقارير الشاملة
                    </h1>
                    <p class="text-muted mb-0">عرض وتحليل التقارير الشاملة للنظام</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="modern-btn modern-btn-outline" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i>
                        <span>تحديث</span>
                    </button>
                </div>
            </div>
                </div>
            </div>
            <!-- فلاتر التقرير -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h6 class="mb-0"><i class="fas fa-filter me-2"></i>فلاتر التقرير</h6>
                </div>
                <div class="modern-card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="type" class="modern-form-label">نوع التقرير</label>
                            <select class="modern-form-control" id="type" name="type">
                                <option value="overview" <?php echo $report_type === 'overview' ? 'selected' : ''; ?>>نظرة عامة</option>
                                <option value="users" <?php echo $report_type === 'users' ? 'selected' : ''; ?>>تقرير المستخدمين</option>
                                <option value="activity" <?php echo $report_type === 'activity' ? 'selected' : ''; ?>>تقرير النشاط</option>
                                <option value="system" <?php echo $report_type === 'system' ? 'selected' : ''; ?>>تقرير النظام</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date_from" class="modern-form-label">من تاريخ</label>
                            <input type="date" class="modern-form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="modern-form-label">إلى تاريخ</label>
                            <input type="date" class="modern-form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="modern-btn modern-btn-primary">
                                <i class="fas fa-search me-1"></i>تطبيق الفلاتر
                            </button>
                        </div>
</form>
                </div>
            </div>
            <?php if ($report_type === 'overview'): ?>
            <!-- تقرير النظرة العامة -->
            <div class="row mb-4">
                <!-- إحصائيات المستخدمين -->
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="modern-card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="stats-label">
                                        إجمالي المستخدمين
                                    </div>
                                    <div class="stats-value">
                                        <?php echo number_format($user_stats['total_users']); ?>
                                    </div>
                                    <div class="stats-label">
                                        نشط: <?php echo $user_stats['active_users']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- إحصائيات المبيعات -->
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="modern-card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="stats-label">
                                        إجمالي المبيعات
                                    </div>
                                    <div class="stats-value">
                                        <?php echo number_format($total_sales); ?>
                                    </div>
                                    <div class="stats-label">
                                        فاتورة مبيعات
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- إحصائيات المشتريات -->
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="modern-card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="stats-label">
                                        إجمالي المشتريات
                                    </div>
                                    <div class="stats-value">
                                        <?php echo number_format($total_purchases); ?>
                                    </div>
                                    <div class="stats-label">
                                        فاتورة شراء
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-truck fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- إحصائيات العملاء -->
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="modern-card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="stats-label">
                                        إجمالي العملاء
                                    </div>
                                    <div class="stats-value">
                                        <?php echo number_format($total_customers); ?>
                                    </div>
                                    <div class="stats-label">
                                        عميل مسجل
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-friends fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- رسم بياني للنشاط -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="modern-card">
                        <div class="modern-card-header">
                            <h6 class="m-0 font-weight-bold text-primary">نشاط المستخدمين</h6>
                        </div>
                        <div class="modern-card-body">
                            <canvas id="activityChart" width="100" height="50"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="modern-card">
                        <div class="modern-card-header">
                            <h6 class="m-0 font-weight-bold text-primary">توزيع البيانات</h6>
                        </div>
                        <div class="modern-card-body">
                            <canvas id="dataChart" width="100" height="50"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </main></div>
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.sidebar {
    min-height: 100vh;
}
.nav-link.active {
    background-color: #495057 !important;
}
</style>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للنشاط
const activityCtx = document.getElementById('activityChart').getContext('2d');
const activityChart = new Chart(activityCtx, {
    type: 'doughnut',
    data: {
        labels: [
    'نشاط اليوم',
    'نشاط الأسبوع',
    'إجمالي النشاط'
],
        datasets: [{
            data: [
                <?php echo $activity_stats['today_activities']; ?>,
                <?php echo $activity_stats['week_activities']; ?>,
                <?php echo $activity_stats['total_activities']; ?>
            ],
            backgroundColor: [
    '#4e73df',
    '#1cc88a',
    '#36b9cc'
]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
// رسم بياني للبيانات
const dataCtx = document.getElementById('dataChart').getContext('2d');
const dataChart = new Chart(dataCtx, {
    type: 'bar',
    data: {
        labels: [t("sales"), t("purchases"), t("customers")],
        datasets: [{
            label: 'العدد',
            data: [
                <?php echo $total_sales; ?>,
                <?php echo $total_purchases; ?>,
                <?php echo $total_customers; ?>
            ],
            backgroundColor: [
    '#1cc88a',
    '#f6c23e',
    '#36b9cc'
]
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
function printReport() {
    window.print();
}

function exportReport() {
    // تصدير التقرير
    alert('سيتم إضافة وظيفة التصدير قريباً');
}
</script>
<?php require_once __DIR__ . '/includes/admin_footer_new.php'; ?>