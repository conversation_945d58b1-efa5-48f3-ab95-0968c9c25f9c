<?php
/**
 * رأس صفحة المدير المتطور - تصميم جديد
 */

// التأكد من تحميل الملفات المطلوبة
if (!function_exists('isAdminLoggedIn')) {
    require_once __DIR__ . '/../config/init.php';
}

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// جلب اسم النظام
$system_name = 'نظام إدارة المبيعات المتطور';
if (function_exists('getSystemSetting')) {
    $system_name = getSystemSetting('system_name', 'نظام إدارة المبيعات المتطور');
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدير - <?php echo htmlspecialchars($system_name); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">

    <style>
        :root {
            /* نظام ألوان متطور */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            
            /* ألوان أساسية */
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #4facfe;
            --warning-color: #43e97b;
            --danger-color: #fa709a;
            --info-color: #a8edea;
            
            /* ألوان النص والخلفية */
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --text-muted: #718096;
            --bg-primary: #ffffff;
            --bg-secondary: #f7fafc;
            --bg-dark: #1a202c;
            --bg-sidebar: #2d3748;
            
            /* ظلال متطورة */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            
            /* حدود دائرية */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-full: 9999px;
            
            /* انتقالات سلسة */
            --transition-fast: all 0.15s ease-out;
            --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* الوضع الداكن المحسن */
        [data-theme="dark"] {
            --text-primary: #f7fafc;
            --text-secondary: #e2e8f0;
            --text-muted: #a0aec0;
            --bg-primary: #1a202c;
            --bg-secondary: #2d3748;
            --bg-sidebar: #1a202c;

            /* ألوان متدرجة للوضع الداكن */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

            /* ظلال للوضع الداكن */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        }

        /* إعادة تعيين أساسية */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-secondary);
            min-height: 100vh;
            font-size: 14px;
            line-height: 1.6;
            color: var(--text-primary);
            overflow-x: hidden;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* الشريط العلوي المتطور */
        .modern-navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: var(--transition-base);
            padding: 1rem 0;
        }

        .modern-navbar .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            letter-spacing: -0.025em;
        }

        .modern-navbar .navbar-brand i {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.75rem;
        }

        /* الشريط الجانبي المتطور */
        .modern-sidebar {
            background: var(--bg-sidebar);
            min-height: calc(100vh - 80px);
            width: 280px;
            position: fixed;
            top: 80px;
            right: 0;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
            box-shadow: var(--shadow-xl);
            border-radius: var(--radius-2xl) 0 0 var(--radius-2xl);
            transition: var(--transition-base);
            z-index: 100;
        }

        .modern-sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .modern-sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .modern-sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: var(--radius-full);
        }

        .modern-sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* عناصر الشريط الجانبي */
        .sidebar-section {
            padding: 1.5rem 1rem 0.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 1rem;
        }

        .sidebar-section:last-child {
            border-bottom: none;
        }

        .sidebar-section-title {
            font-size: 0.75rem;
            font-weight: 700;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            letter-spacing: 0.1em;
            margin-bottom: 1rem;
            padding: 0 1rem;
        }

        .sidebar-nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            margin: 0.25rem 1rem;
            border-radius: var(--radius-xl);
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: var(--transition-base);
            position: relative;
            overflow: hidden;
        }

        .sidebar-nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary-gradient);
            opacity: 0;
            transition: var(--transition-base);
            border-radius: var(--radius-xl);
        }

        .sidebar-nav-link:hover {
            color: white;
            transform: translateX(4px);
            box-shadow: var(--shadow-lg);
        }

        .sidebar-nav-link:hover::before {
            opacity: 0.15;
        }

        .sidebar-nav-link.active {
            color: white;
            background: var(--primary-gradient);
            box-shadow: var(--shadow-lg);
            transform: translateX(4px);
        }

        .sidebar-nav-link.active::before {
            opacity: 1;
        }

        .sidebar-nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1.125rem;
            transition: var(--transition-base);
        }

        .sidebar-nav-link:hover i,
        .sidebar-nav-link.active i {
            transform: scale(1.1);
        }

        /* البطاقات المتطورة */
        .modern-card {
            background: var(--bg-primary);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: var(--transition-base);
            overflow: hidden;
            position: relative;
        }

        .modern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            opacity: 0;
            transition: var(--transition-base);
        }

        .modern-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-2xl);
        }

        .modern-card:hover::before {
            opacity: 1;
        }

        .modern-card-header {
            padding: 1.5rem 2rem 1rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        }

        .modern-card-body {
            padding: 2rem;
        }

        .modern-card-footer {
            padding: 1rem 2rem 1.5rem;
            background: rgba(0, 0, 0, 0.02);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* الأزرار المتطورة */
        .modern-btn {
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius-lg);
            font-weight: 600;
            font-size: 0.875rem;
            border: none;
            cursor: pointer;
            transition: var(--transition-base);
            position: relative;
            overflow: hidden;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .modern-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: var(--transition-fast);
        }

        .modern-btn:hover::before {
            left: 100%;
        }

        .modern-btn-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .modern-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            color: white;
        }

        .modern-btn-secondary {
            background: var(--secondary-gradient);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .modern-btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            color: white;
        }

        .modern-btn-success {
            background: var(--success-gradient);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .modern-btn-success:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            color: white;
        }

        .modern-btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            box-shadow: none;
        }

        .modern-btn-outline:hover {
            background: var(--primary-gradient);
            color: white;
            border-color: transparent;
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* الجداول المتطورة */
        .modern-table {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .modern-table thead th {
            background: var(--primary-gradient);
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            padding: 1rem 1.5rem;
            border: none;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .modern-table tbody td {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            vertical-align: middle;
            font-size: 0.875rem;
        }

        .modern-table tbody tr:last-child td {
            border-bottom: none;
        }

        .modern-table tbody tr:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            transform: scale(1.01);
            transition: var(--transition-base);
        }

        /* النماذج المتطورة */
        .modern-form-control {
            background: var(--bg-primary);
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: var(--radius-lg);
            padding: 0.875rem 1.25rem;
            font-size: 0.875rem;
            transition: var(--transition-base);
            width: 100%;
        }

        .modern-form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .modern-form-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        /* بطاقات الإحصائيات المتطورة */
        .stats-card {
            background: var(--bg-primary);
            border-radius: var(--radius-2xl);
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: var(--transition-base);
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-2xl);
        }

        .stats-value {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
            margin-bottom: 0.5rem;
        }

        .stats-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .stats-change {
            font-size: 0.75rem;
            font-weight: 600;
            margin-top: 0.5rem;
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            background: var(--primary-gradient);
            box-shadow: var(--shadow-lg);
        }

        /* التأثيرات المتطورة */
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .gradient-text {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hover-lift {
            transition: var(--transition-base);
        }

        .hover-lift:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* الشارات المتطورة */
        .modern-badge {
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius-full);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .modern-badge-primary {
            background: var(--primary-gradient);
            color: white;
        }

        .modern-badge-success {
            background: var(--success-gradient);
            color: white;
        }

        .modern-badge-warning {
            background: var(--warning-gradient);
            color: var(--text-primary);
        }

        .modern-badge-danger {
            background: var(--danger-gradient);
            color: white;
        }

        /* الاستجابة للشاشات المختلفة */
        @media (max-width: 992px) {
            .modern-sidebar {
                position: fixed;
                top: 0;
                right: -100%;
                width: 280px;
                height: 100vh;
                z-index: 1000;
                border-radius: 0;
                transition: var(--transition-base);
            }

            .modern-sidebar.show {
                right: 0;
            }

            .main-content,
            .admin-content {
                margin-right: 0;
                padding: 1rem;
            }
        }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(4px);
                z-index: 999;
                opacity: 0;
                visibility: hidden;
                transition: var(--transition-base);
            }

            .sidebar-overlay.show {
                opacity: 1;
                visibility: visible;
            }

            .modern-card-body {
                padding: 1.5rem;
            }

            .stats-card {
                padding: 1.5rem;
            }

            .stats-value {
                font-size: 2rem;
            }
        

        @media (max-width: 576px) {
            .modern-navbar .navbar-brand {
                font-size: 1.25rem;
            }

            .modern-card-body {
                padding: 1rem;
            }

            .stats-card {
                padding: 1rem;
            }

            .stats-value {
                font-size: 1.75rem;
            }

            .modern-btn {
                padding: 0.625rem 1.25rem;
                font-size: 0.8rem;
            }
        }

        /* الوضع الداكن المحسن */
        [data-theme="dark"] {
            /* ألوان متدرجة للوضع الداكن */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

            /* ظلال للوضع الداكن */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
        }

        [data-theme="dark"] .modern-navbar {
            background: rgba(26, 32, 44, 0.95);
            border-bottom-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .modern-card {
            background: var(--bg-primary);
            border-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .modern-table {
            background: var(--bg-primary);
            border-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .modern-table tbody td {
            border-bottom-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .modern-table tbody tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        [data-theme="dark"] .modern-form-control {
            background: var(--bg-primary);
            border-color: rgba(255, 255, 255, 0.2);
            color: var(--text-primary);
        }

        [data-theme="dark"] .modern-form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

        [data-theme="dark"] .stats-card {
            background: var(--bg-primary);
            border-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .modern-sidebar {
            background: var(--bg-sidebar);
            border-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .dropdown-menu {
            background: var(--bg-primary);
            border-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .dropdown-item {
            color: var(--text-secondary);
        }

        [data-theme="dark"] .dropdown-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        [data-theme="dark"] .modal-content {
            background: var(--bg-primary);
            border-color: rgba(255, 255, 255, 0.1);
        }

        /* تحسينات الأداء */
        .modern-card,
        .modern-btn,
        .stats-card,
        .sidebar-nav-link {
            will-change: transform;
        }

        /* تأثيرات إضافية */
        .floating-action {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            border-radius: var(--radius-full);
            background: var(--primary-gradient);
            color: white;
            border: none;
            box-shadow: var(--shadow-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            cursor: pointer;
            transition: var(--transition-base);
            z-index: 1000;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-2xl);
        }

        /* إصلاح تخطيط المحتوى الرئيسي */
        .main-content {
            margin-right: 300px;
            min-height: calc(100vh - 80px);
            padding: 1.5rem;
            transition: var(--transition-base);
        }

        /* تحسين التخطيط العام */
        .admin-layout {
            display: flex;
            min-height: calc(100vh - 80px);
        }

        .admin-content {
            flex: 1;
            margin-right: 300px;
            padding: 1.5rem;
            overflow-x: hidden;
        }

        /* إصلاح مشكلة التمرير */
        body {
            overflow-x: hidden;
        }

        .container-fluid {
            padding: 0;
            margin: 0;
            max-width: 100%;
        }
    </style>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        // تبديل الوضع الداكن/الفاتح المحسن
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            // تطبيق الوضع الجديد
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // تحديث أيقونة الزر
            updateThemeIcon(newTheme);

            // تأثير انتقال سلس
            document.body.style.transition = 'all 0.3s ease';
            setTimeout(() => {
                document.body.style.transition = '';
            }, 300);
        }

        // تحديث أيقونة الوضع
        function updateThemeIcon(theme) {
            const themeButtons = document.querySelectorAll('[onclick="toggleTheme()"] i');
            themeButtons.forEach(icon => {
                if (theme === 'dark') {
                    icon.className = 'fas fa-sun';
                } else {
                    icon.className = 'fas fa-moon';
                }
            });
        }

        // تحميل الوضع المحفوظ
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeIcon(savedTheme);
        });

        // تبديل الشريط الجانبي في الشاشات الصغيرة
        function toggleSidebar() {
            const sidebar = document.querySelector('.modern-sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            if (sidebar && overlay) {
                sidebar.classList.toggle('show');
                overlay.classList.toggle('show');
            }
        }

        // إغلاق الشريط الجانبي عند النقر على الخلفية
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('sidebar-overlay')) {
                toggleSidebar();
            }
        });
    </script>
</head>
<body>

<!-- الشريط العلوي -->
<nav class="navbar navbar-expand-lg modern-navbar">
    <div class="container-fluid">
        <a class="navbar-brand" href="admin_dashboard.php">
            <i class="fas fa-crown"></i>
            <span><?php echo htmlspecialchars($system_name); ?></span>
        </a>

        <div class="d-flex align-items-center gap-3">
            <!-- زر تبديل الوضع -->
            <button class="modern-btn modern-btn-outline" onclick="toggleTheme()" title="تبديل الوضع">
                <i class="fas fa-moon"></i>
            </button>

            <!-- زر الشريط الجانبي للشاشات الصغيرة -->
            <button class="modern-btn modern-btn-primary d-lg-none" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>

            <!-- قائمة المستخدم -->
            <div class="dropdown">
                <button class="modern-btn modern-btn-outline dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user-circle"></i>
                    <span>المدير</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="admin_system.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="admin_logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<!-- خلفية الشريط الجانبي للشاشات الصغيرة -->
<div class="sidebar-overlay"></div>
