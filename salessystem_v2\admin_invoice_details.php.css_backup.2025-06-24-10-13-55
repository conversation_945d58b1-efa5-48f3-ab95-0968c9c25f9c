<?php
/**
 * صفحة تفاصيل الفواتير للمدير
 */
require_once __DIR__ . '/config/init.php';
// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}
// التحقق من الصلاحيات
if (!hasAdminPermission('view_all_data')) {
    $_SESSION[
    'error'
] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}
global $main_db;
$user_id = intval($_GET[
    'user_id'
] ?? 0);
$invoice_type = $_GET[
    'type'
] ?? 'sales';
$date_from = $_GET[
    'date_from'
] ?? date('Y-m-01');
$date_to = $_GET[
    'date_to'
] ?? date('Y-m-d');
// جلب معلومات المستخدم
$user_info = null;
if ($user_id > 0) {
    $stmt = $main_db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $user_info = $stmt->get_result()->fetch_assoc();
    $stmt->close();
}
if (!$user_info) {
    $_SESSION[
    'error'
] = 'المستخدم غير موجود';
    header("Location: admin_users.php");
    exit();
}
// الاتصال بقاعدة بيانات المستخدم
$user_db_name = "sales_system_user_" . $user_id;
$user_db = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS, $user_db_name);
if ($user_db->connect_error) {
    $_SESSION[
    'error'
] = 'لا يمكن الوصول لقاعدة بيانات المستخدم';
    header("Location: admin_users.php");
    exit();
}
// جلب الفواتير حسب النوع
$invoices = [];
if ($invoice_type === 'sales') {
    $query = "SELECT s.*, c.name as customer_name, c.phone as customer_phone
              FROM sales s
              LEFT JOIN customers c ON s.customer_id = c.id
              WHERE s.date BETWEEN ? AND ?
              ORDER BY s.date DESC, s.id DESC";
}
else {
    $query = "SELECT p.*, p.supplier_name
              FROM purchases p
              WHERE p.date BETWEEN ? AND ?
              ORDER BY p.date DESC, p.id DESC";
}
$stmt = $user_db->prepare($query);
$stmt->bind_param("ss", $date_from, $date_to);
$stmt->execute();
$invoices_result = $stmt->get_result();
require_once __DIR__ . '/includes/admin_header_new.php';
?>
<div class="admin-layout">
        <!-- الشريط الجانبي -->
        <nav class="modern-sidebar d-none d-lg-block"><div class="sidebar-section">
<div class="sidebar-section-title">الإدارة الرئيسية</div><a class="sidebar-nav-link" href="admin_dashboard.php"><i class="fas fa-tachometer-alt"></i><span>لوحة التحكم</span></a><a class="sidebar-nav-link" href="admin_users.php"><i class="fas fa-users"></i><span>إدارة المستخدمين</span></a><a class="sidebar-nav-link" href="admin_activity.php"><i class="fas fa-history"></i><span>سجل العمليات</span></a></div>
<div class="sidebar-section">
<div class="sidebar-section-title">التقارير والإحصائيات</div><a class="sidebar-nav-link" href="admin_reports.php"><i class="fas fa-chart-bar"></i><span>التقارير الشاملة</span></a><a class="sidebar-nav-link" href="admin_financial.php"><i class="fas fa-file-invoice-dollar"></i><span>التقارير المالية</span></a></div>
<div class="sidebar-section">
<div class="sidebar-section-title">إدارة النظام</div><a class="sidebar-nav-link" href="admin_error_logs.php"><i class="fas fa-exclamation-triangle"></i><span>سجل الأخطاء</span></a><a class="sidebar-nav-link" href="admin_system.php"><i class="fas fa-cogs"></i><span>إعدادات النظام</span></a></div>
<div class="sidebar-section">
<div class="sidebar-section-title">إدارة المديرين</div><a class="sidebar-nav-link" href="admin_manage_admins.php"><i class="fas fa-user-shield"></i><span>إدارة المديرين</span></a></div></nav>
        <!-- المحتوى الرئيسي -->
        <main class="admin-content">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-2 fw-bold gradient-text">
                        <i class="fas fa-file-invoice me-3"></i>
                        تفاصيل الفاتورة
                    </h1>
                    <p class="text-muted mb-0">عرض تفاصيل الفاتورة الكاملة</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="modern-btn modern-btn-outline" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i>
                        <span>تحديث</span>
                    </button>
                </div>
            </div>
            </div>
            <!-- معلومات المستخدم -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-user me-2"></i>معلومات المستخدم
                    </h6>
                </div>
                <div class="modern-card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>الاسم الكامل:</strong><br>
                            <?php echo htmlspecialchars($user_info[
    'full_name'
]); ?>
                        </div>
                        <div class="col-md-3">
                            <strong>اسم المستخدم:</strong><br>
                            <?php echo htmlspecialchars($user_info[
    'username'
]); ?>
                        </div>
                        <div class="col-md-3">
                            <strong>البريد الإلكتروني:</strong><br>
                            <?php echo htmlspecialchars($user_info[
    'email'
]); ?>
                        </div>
                        <div class="col-md-3">
                            <strong>تاريخ التسجيل:</strong><br>
                            <?php echo date('Y-m-d', strtotime($user_info[
    'created_at'
])); ?>
                        </div>
                    </div>
                </div>
            </div>
            <!-- فلاتر البحث -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h6 class="mb-0"><i class="fas fa-filter me-2"></i>فلاتر البحث</h6>
                </div>
                <div class="modern-card-body">
                    <form method="GET" class="row g-3">
                        <input type="hidden" name="user_id" value="<?php echo $user_id; ?>">
                        <div class="col-md-3">
                            <label for="type" class="modern-form-label">نوع الفاتورة</label>
                            <select class="form-select" id="type" name="type">
                                <option value="sales" <?php echo $invoice_type === 'sales' ? 'selected' : ''; ?>>فواتير المبيعات</option>
                                <option value="purchases" <?php echo $invoice_type === 'purchases' ? 'selected' : ''; ?>>فواتير المشتريات</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date_from" class="modern-form-label">من تاريخ</label>
                            <input type="date" class="modern-form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="modern-form-label">إلى تاريخ</label>
                            <input type="date" class="modern-form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="modern-btn modern-btn-primary">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                        </div>
</form>
                </div>
            </div>
            <!-- جدول الفواتير -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        فواتير <?php echo $invoice_type === 'sales' ? 'المبيعات' : 'المشتريات'; ?>
                    </h6>
                    <span class="modern-badge modern-badge-info">
                        إجمالي: <?php echo $invoices_result->num_rows; ?> فاتورة
                    </span>
                </div>
                <div class="modern-card-body">
                    <div class="modern-table table-responsive">
                        <table class="modern-table table table-bordered table-hover" width="100%" cellspacing="0">
                            <thead class="modern-table table-dark">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <?php if ($invoice_type === 'sales'): ?>
                                    <th>العميل</th>
                                    <th>هاتف العميل</th>
                                    <?php else: ?>
                                    <th>المورد</th>
                                    <th>هاتف المورد</th>
                                    <?php endif; ?>
                                    <th>المبلغ الفرعي</th>
                                    <th>الضريبة</th>
                                    <th>الإجمالي</th>
                                    <th>حالة الدفع</th>
                                    <th>الإجراءات</th>
</tr>
                            </thead>
                            <tbody>
                                <?php
                                $total_subtotal = 0;
                                $total_tax = 0;
                                $total_amount = 0;
                                while ($invoice = $invoices_result->fetch_assoc()):
                                    $total_subtotal += $invoice[
    'subtotal'
];
                                    $total_tax += $invoice[
    'tax_amount'
];
                                    $total_amount += $invoice[
    'total_amount'
];
                                ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($invoice[
    'invoice_number'
]); ?></strong>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($invoice[
    'date'
])); ?></td>
                                    <?php if ($invoice_type === 'sales'): ?>
                                    <td><?php echo htmlspecialchars($invoice[
    'customer_name'
] ?? 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($invoice[
    'customer_phone'
] ?? '-'); ?></td>
                                    <?php else: ?>
                                    <td><?php echo htmlspecialchars($invoice[
    'supplier_name'
]); ?></td>
                                    <td><?php echo htmlspecialchars($invoice[
    'supplier_phone'
] ?? '-'); ?></td>
                                    <?php endif; ?>
                                    <td class="text-end"><?php echo number_format($invoice[
    'subtotal'
], 2); ?> ر.س</td>
                                    <td class="text-end"><?php echo number_format($invoice[
    'tax_amount'
], 2); ?> ر.س</td>
                                    <td class="text-end">
                                        <strong><?php echo number_format($invoice[
    'total_amount'
], 2); ?> ر.س</strong>
                                    </td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        $status_text = '';
                                        switch ($invoice[
    'payment_status'
]) {
                                            case 'paid':
                                                $status_class = 'bg-success';
                                                $status_text = 'مدفوع';
                                                break;
                                            case 'partial':
                                                $status_class = 'bg-warning';
                                                $status_text = 'مدفوع جزئياً';
                                                break;
                                            default:
                                                $status_class = 'bg-danger';
                                                $status_text = 'غير مدفوع';
                                        }
                                        ?>
                                        <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="admin_invoice_view.php?user_id=<?php echo $user_id; ?>&type=<?php echo $invoice_type; ?>&id=<?php echo $invoice[
    'id'
]; ?>"
                                               class="modern-btn modern-btn-sm btn-info" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="admin_invoice_print.php?user_id=<?php echo $user_id; ?>&type=<?php echo $invoice_type; ?>&id=<?php echo $invoice[
    'id'
]; ?>"
                                               class="modern-btn modern-btn-sm btn-secondary" title="طباعة" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                        </div>
                                    </td>
</tr>
                                <?php endwhile; ?>
                            </tbody>
                            <tfoot class="modern-table table-secondary">
                                <tr>
                                    <th colspan="<?php echo $invoice_type === 'sales' ? '5' : '5'; ?>">الإجمالي</th>
                                    <th class="text-end"><?php echo number_format($total_subtotal, 2); ?> ر.س</th>
                                    <th class="text-end"><?php echo number_format($total_tax, 2); ?> ر.س</th>
                                    <th class="text-end"><strong><?php echo number_format($total_amount, 2); ?> ر.س</strong></th>
                                    <th colspan="2">-</th>
</tr>
                            </tfoot>
</table>
                    </div>
                </div>
            </div>
        </main></div>
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.sidebar {
    min-height: 100vh;
}
.nav-link.active {
    background-color: #495057 !important;
}
</style>
<?php
$user_db->close();
require_once __DIR__ . '/includes/admin_footer_new.php';
?>